/**
 * React Context for calculation detail page state management
 * Eliminates prop drilling by providing centralized state access
 *
 * This file only exports React components to satisfy Fast Refresh requirements.
 * Hooks are exported from ./calculationHooks.ts
 * Constants are exported from ./calculationConstants.ts
 */
import React, { useMemo, ReactNode } from "react";
import { CalculationContext } from "./context";
import {
  CalculationDetailState,
  CalculationDetailActions,
} from "../types/calculationState";

/**
 * Provider props interface
 */
interface CalculationProviderProps {
  children: ReactNode;
  calculationId: string;
  state: CalculationDetailState;
  actions: CalculationDetailActions;
}

/**
 * Provider component with memoization for performance
 */
export const CalculationProvider: React.FC<CalculationProviderProps> = ({
  children,
  calculationId,
  state,
  actions,
}) => {
  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => {
    return {
      calculationId,
      state,
      actions,
    };
  }, [calculationId, state, actions]);

  return (
    <CalculationContext.Provider value={contextValue}>
      {children}
    </CalculationContext.Provider>
  );
};
