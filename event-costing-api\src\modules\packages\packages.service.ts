import {
  Injectable,
  Logger,
  InternalServerErrorException,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { SupabaseClient, PostgrestError } from '@supabase/supabase-js';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { CacheService } from '../../core/cache/cache.service';
import {
  ListPackageVariationsDto,
  PackageSortField,
  SortDirection,
} from './dto/list-package-variations.dto';
import { PackageVariationDto } from './dto/package-variation.dto';
import { PackageOptionDetailDto } from './dto/package-option-detail.dto';
import { PaginatedResponseDto } from '../../shared/dtos/paginated-response.dto';
import {
  PackagesByCategoryResponseDto,
  CategoryWithPackagesDto,
  CategoryPackageDto,
  CategoryPackageOptionDto,
} from './dto/packages-by-category-response.dto';
import {
  BatchPackageOptionsResponseDto,
  BatchPackageOptionDto,
} from './dto/batch-package-options-response.dto';

// Define interface for nested price data
interface PackagePriceData {
  price: number;
  unit_base_cost: number;
}

// Define interface for the main package data structure returned by the query
interface PackageWithPrice {
  id: string;
  name: string;
  description: string | null;
  category_id: string;
  quantity_basis: 'fixed' | 'attendees' | 'days' | 'attendees_days'; // Use specific enum values
  package_prices: PackagePriceData[]; // Supabase relations are typically arrays
}

// Define interface for the city availability check query
interface PackageCityId {
  package_id: string;
}

// Define interface for the venue availability check query
interface PackageVenueId {
  package_id: string;
}

// Define interface for the conflict check query
interface PackageDependencyData {
  package_id: string;
  depends_on_package_id: string;
}

@Injectable()
export class PackagesService {
  private readonly logger = new Logger(PackagesService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly cacheService: CacheService,
  ) {}

  async findVariations(
    queryDto: ListPackageVariationsDto,
  ): Promise<PaginatedResponseDto<PackageVariationDto>> {
    this.logger.log(
      `Finding package variations with query: ${JSON.stringify(queryDto)}`,
    );

    // Generate a cache key based on the query parameters
    const cacheKey = this.generatePackageVariationsCacheKey(queryDto);

    // Try to get from cache first
    return this.cacheService.getOrSet(
      cacheKey,
      () => this.fetchPackageVariations(queryDto),
      3600, // Cache for 1 hour
    );
  }

  /**
   * Sort package variations based on the specified field and direction
   */
  private sortPackageVariations(
    packages: PackageVariationDto[],
    sortBy: PackageSortField,
    sortOrder: SortDirection,
  ): PackageVariationDto[] {
    const sortMultiplier = sortOrder === SortDirection.ASC ? 1 : -1;

    return [...packages].sort((a, b) => {
      switch (sortBy) {
        case PackageSortField.NAME:
          return sortMultiplier * a.name.localeCompare(b.name);
        case PackageSortField.PRICE:
          return sortMultiplier * (a.price - b.price);
        case PackageSortField.CATEGORY:
          return sortMultiplier * a.category_id.localeCompare(b.category_id);
        default:
          return 0;
      }
    });
  }

  /**
   * Generate a cache key for package variations query
   * @param queryDto - The query parameters
   * @returns A unique cache key
   */
  private generatePackageVariationsCacheKey(
    queryDto: ListPackageVariationsDto,
  ): string {
    const {
      categoryId,
      cityId,
      venueId,
      venueIds,
      currencyId,
      currentSelectionIds,
      search,
      sortBy,
      sortOrder,
      limit,
      offset,
    } = queryDto;

    // Create a normalized representation of the query for the cache key
    const keyParts = [
      `categoryId:${categoryId || 'null'}`,
      `cityId:${cityId || 'null'}`,
      `venueId:${venueId || 'null'}`,
      `venueIds:${venueIds ? venueIds.sort().join(',') : 'null'}`,
      `currencyId:${currencyId}`,
      `currentSelectionIds:${currentSelectionIds ? currentSelectionIds.sort().join(',') : 'null'}`,
      `search:${search || 'null'}`,
      `sortBy:${sortBy || 'name'}`,
      `sortOrder:${sortOrder || 'asc'}`,
      `limit:${limit || 20}`,
      `offset:${offset || 0}`,
    ];

    return `package-variations:${keyParts.join(';')}`;
  }

  /**
   * Fetch package variations from the database
   * @param queryDto - The query parameters
   * @returns Paginated package variations
   */
  private async fetchPackageVariations(
    queryDto: ListPackageVariationsDto,
  ): Promise<PaginatedResponseDto<PackageVariationDto>> {
    const {
      categoryId,
      cityId,
      venueId,
      venueIds,
      currencyId,
      currentSelectionIds,
      search,
      sortBy = PackageSortField.NAME,
      sortOrder = SortDirection.ASC,
      limit = 20,
      offset = 0,
    } = queryDto;

    // Validate that we have either venueId or venueIds, not both
    if (venueId && venueIds && venueIds.length > 0) {
      throw new BadRequestException(
        'Cannot specify both venueId and venueIds. Use venueIds for multiple venues.',
      );
    }

    // Use venueId as a single item in venueIds if provided
    const effectiveVenueIds = venueId ? [venueId] : venueIds;

    const supabase = this.supabaseService.getClient();

    // Base query for packages with their price for the specified currency
    let query = supabase
      .from('packages')
      .select(
        `
                id,
                name,
                description,
                category_id,
                quantity_basis,
                package_prices!inner(price, unit_base_cost)
            `,
        { count: 'exact' },
      )
      // Filter for non-deleted packages using the correct column name
      .eq('is_deleted', false)
      .eq('package_prices.currency_id', currencyId);

    // Apply category filter
    if (categoryId) {
      query = query.eq('category_id', categoryId);
    }

    // Apply search filter if provided
    if (search) {
      query = query.ilike('name', `%${search}%`);
    }

    // Execute the base query to get packages and prices
    const {
      data,
      error: pkgError,
      count,
    }: {
      data: PackageWithPrice[] | null;
      error: PostgrestError | null;
      count: number | null;
    } = await query;

    if (pkgError) {
      this.logger.error(
        `Error fetching packages/prices for currency ${currencyId}: ${pkgError.message}`,
        pkgError.stack,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve package variations.',
      );
    }

    if (!data) {
      this.logger.error('Query returned null data for packagesWithPrices');
      throw new InternalServerErrorException(
        'Failed to retrieve package variations (null data).',
      );
    }

    const packagesWithPrices: PackageWithPrice[] = data; // Now correctly typed

    if (!packagesWithPrices || packagesWithPrices.length === 0) {
      this.logger.log(
        `No packages found matching initial criteria (category/currency price).`,
      );
      return {
        data: [],
        count: 0,
        limit,
        offset,
      };
    }

    // Prepare for city, venue, and dependency checks
    const packageIds = packagesWithPrices.map(p => p.id);
    let cityAvailabilityMap = new Map<string, boolean>();
    let venueAvailabilityMap = new Map<string, boolean>();
    let conflictMap = new Map<string, boolean>();

    // Check city availability if cityId is provided
    if (cityId) {
      cityAvailabilityMap = await this.checkCityAvailability(
        supabase,
        packageIds,
        cityId,
      );
    }

    // Check venue availability if venueIds are provided
    if (effectiveVenueIds && effectiveVenueIds.length > 0) {
      venueAvailabilityMap = await this.checkVenuesAvailability(
        supabase,
        packageIds,
        effectiveVenueIds,
      );
    }

    // Check conflicts if currentSelectionIds are provided
    if (currentSelectionIds && currentSelectionIds.length > 0) {
      conflictMap = await this.checkConflicts(
        supabase,
        packageIds,
        currentSelectionIds,
      );
    }

    // Map results to DTO
    const results: PackageVariationDto[] = packagesWithPrices.map(pkg => {
      const priceData = pkg.package_prices[0];

      const isAvailableInCity =
        !cityId || cityAvailabilityMap.get(pkg.id) || false;
      const isAvailableInVenue =
        !effectiveVenueIds ||
        effectiveVenueIds.length === 0 ||
        venueAvailabilityMap.get(pkg.id) ||
        false;
      const conflicts = conflictMap.get(pkg.id) || false;

      return {
        package_id: pkg.id,
        name: pkg.name,
        description: pkg.description,
        category_id: pkg.category_id,
        quantity_basis: pkg.quantity_basis,
        price: priceData.price,
        unit_base_cost: priceData.unit_base_cost,
        is_available_in_city: isAvailableInCity,
        is_available_in_venue: isAvailableInVenue,
        conflicts_with_selection: conflicts,
      };
    });

    // Filter out packages not available in the specified city or venue
    let filteredResults = results;

    // Apply city filter if provided
    if (cityId) {
      filteredResults = filteredResults.filter(r => r.is_available_in_city);
    }

    // Apply venue filter if provided
    if (effectiveVenueIds && effectiveVenueIds.length > 0) {
      filteredResults = filteredResults.filter(r => r.is_available_in_venue);
    }

    // Apply sorting
    const sortedResults = this.sortPackageVariations(
      filteredResults,
      sortBy,
      sortOrder,
    );

    // Apply pagination
    const paginatedResults = sortedResults.slice(offset, offset + limit);

    this.logger.log(
      `Found ${filteredResults.length} package variations matching criteria. Returning ${paginatedResults.length} items.`,
    );

    return {
      data: paginatedResults,
      count: filteredResults.length,
      limit,
      offset,
    };
  }

  async findOptions(
    packageId: string,
    currencyId?: string,
    venueId?: string,
  ): Promise<PackageOptionDetailDto[]> {
    this.logger.log(
      `Fetching options for package ${packageId}${currencyId ? `, currency ${currencyId}` : ''}${venueId ? `, venue ${venueId}` : ''}`,
    );

    // Generate a cache key for the options
    const cacheKey = `package-options:${packageId}:${currencyId || 'all'}:${venueId || 'all'}`;

    // Try to get from cache first
    return this.cacheService.getOrSet(
      cacheKey,
      () => this.fetchPackageOptions(packageId, currencyId, venueId),
      3600, // Cache for 1 hour
    );
  }

  /**
   * Fetch package options from the database
   * @param packageId - The package ID
   * @param currencyId - The currency ID (optional)
   * @param venueId - The venue ID (optional)
   * @returns Package options
   */
  private async fetchPackageOptions(
    packageId: string,
    currencyId?: string,
    venueId?: string,
  ): Promise<PackageOptionDetailDto[]> {
    const supabase = this.supabaseService.getClient();

    // Start building the query
    let query = supabase
      .from('package_options')
      .select('id, option_name, description, price_adjustment, cost_adjustment')
      .eq('applicable_package_id', packageId);

    // Add currency filter if provided
    if (currencyId) {
      query = query.eq('currency_id', currencyId);
    }

    // Add venue filter if provided
    if (venueId) {
      // This assumes there's a relationship between package_options and venues
      // Adjust this query based on your actual database schema
      query = query.eq('venue_id', venueId);
    }

    // Add ordering
    query = query.order('option_name');

    // Execute the query
    const { data, error } = await query;

    if (error) {
      this.logger.error(
        `Error fetching options for package ${packageId}${currencyId ? `, currency ${currencyId}` : ''}${venueId ? `, venue ${venueId}` : ''}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve package options.',
      );
    }

    if (!data || data.length === 0) {
      this.logger.log(
        `No active options found for package ${packageId}${currencyId ? `, currency ${currencyId}` : ''}${venueId ? `, venue ${venueId}` : ''}`,
      );
      return [];
    }

    return data;
  }

  // --- Helper methods for findVariations ---

  private async checkCityAvailability(
    supabase: SupabaseClient,
    packageIds: string[],
    cityId: string,
  ): Promise<Map<string, boolean>> {
    const availabilityMap = new Map<string, boolean>();
    packageIds.forEach(id => availabilityMap.set(id, false)); // Initialize all as unavailable

    const { data, error } = await supabase
      .from('package_cities')
      .select('package_id')
      .in('package_id', packageIds)
      .eq('city_id', cityId)
      .returns<PackageCityId[]>(); // Explicitly type the return

    if (error) {
      this.logger.error(
        `Error checking city availability for city ${cityId}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Failed check package city availability.',
      );
    }

    data?.forEach(row => {
      // 'row' is now correctly typed as PackageCityId
      availabilityMap.set(row.package_id, true);
    });

    return availabilityMap;
  }

  private async checkVenueAvailability(
    supabase: SupabaseClient,
    packageIds: string[],
    venueId: string,
  ): Promise<Map<string, boolean>> {
    const availabilityMap = new Map<string, boolean>();
    packageIds.forEach(id => availabilityMap.set(id, false)); // Initialize all as unavailable

    const { data, error } = await supabase
      .from('package_venues')
      .select('package_id')
      .in('package_id', packageIds)
      .eq('venue_id', venueId)
      .returns<PackageVenueId[]>(); // Explicitly type the return

    if (error) {
      this.logger.error(
        `Error checking venue availability for venue ${venueId}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Failed check package venue availability.',
      );
    }

    data?.forEach(row => {
      // 'row' is now correctly typed as PackageVenueId
      availabilityMap.set(row.package_id, true);
    });

    return availabilityMap;
  }

  /**
   * Check availability of packages in multiple venues
   * A package is considered available if it's available in ANY of the specified venues
   */
  private async checkVenuesAvailability(
    supabase: SupabaseClient,
    packageIds: string[],
    venueIds: string[],
  ): Promise<Map<string, boolean>> {
    const availabilityMap = new Map<string, boolean>();
    packageIds.forEach(id => availabilityMap.set(id, false)); // Initialize all as unavailable

    if (venueIds.length === 0) {
      return availabilityMap;
    }

    const { data, error } = await supabase
      .from('package_venues')
      .select('package_id, venue_id')
      .in('package_id', packageIds)
      .in('venue_id', venueIds)
      .returns<{ package_id: string; venue_id: string }[]>();

    if (error) {
      this.logger.error(
        `Error checking venue availability for venues [${venueIds.join(', ')}]: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Failed to check package venue availability.',
      );
    }

    // A package is available if it appears in the results for any of the specified venues
    data?.forEach(row => {
      availabilityMap.set(row.package_id, true);
    });

    return availabilityMap;
  }

  private async checkConflicts(
    supabase: SupabaseClient,
    packageIds: string[],
    currentSelectionIds: string[],
  ): Promise<Map<string, boolean>> {
    const conflictMap = new Map<string, boolean>();
    packageIds.forEach(id => conflictMap.set(id, false)); // Initialize all as non-conflicting

    // Fetch dependencies where EITHER the package OR its dependency is in our list of packageIds,
    // AND the OTHER side of the dependency is in the currentSelectionIds.
    const { data, error } = await supabase
      .from('package_dependencies')
      .select('package_id, depends_on_package_id')
      .or(
        `and(package_id.in.(${packageIds.join(',')}),depends_on_package_id.in.(${currentSelectionIds.join(',')})),and(depends_on_package_id.in.(${packageIds.join(',')}),package_id.in.(${currentSelectionIds.join(',')}))`,
      )
      .returns<PackageDependencyData[]>(); // Explicitly type the return
    // This complex .or attempts to find conflicts in both directions

    if (error) {
      this.logger.error(
        `Error checking package conflicts: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Failed check package conflicts.');
    }

    data?.forEach(dep => {
      // 'dep' is now correctly typed as PackageDependencyData
      if (
        dep.package_id &&
        packageIds.includes(dep.package_id) &&
        currentSelectionIds.includes(dep.depends_on_package_id)
      ) {
        conflictMap.set(dep.package_id, true);
      }

      if (
        dep.depends_on_package_id &&
        packageIds.includes(dep.depends_on_package_id) &&
        currentSelectionIds.includes(dep.package_id)
      )
        conflictMap.set(dep.depends_on_package_id, true);
    });

    return conflictMap;
  }

  /**
   * Get packages organized by category
   * @param currencyId - The currency ID
   * @param cityId - The city ID (optional)
   * @param venueId - The venue ID (optional)
   * @param includeOptions - Whether to include package options (optional)
   * @returns Packages organized by category
   */
  async getPackagesByCategory(
    currencyId: string,
    cityId?: string,
    venueId?: string,
    includeOptions: boolean = false,
  ): Promise<PackagesByCategoryResponseDto> {
    this.logger.log(
      `Getting packages by category for currency ${currencyId}, city ${cityId || 'any'}, venue ${venueId || 'any'}`,
    );

    // Generate a cache key
    const cacheKey = `packages-by-category:${currencyId}:${cityId || 'all'}:${venueId || 'all'}:${includeOptions}`;

    // Try to get from cache first
    return this.cacheService.getOrSet(
      cacheKey,
      () =>
        this.fetchPackagesByCategory(
          currencyId,
          cityId,
          venueId,
          includeOptions,
        ),
      3600, // Cache for 1 hour
    );
  }

  /**
   * Fetch packages organized by category from the database
   * @param currencyId - The currency ID
   * @param cityId - The city ID (optional)
   * @param venueId - The venue ID (optional)
   * @param includeOptions - Whether to include package options (optional)
   * @returns Packages organized by category
   */
  private async fetchPackagesByCategory(
    currencyId: string,
    cityId?: string,
    venueId?: string,
    includeOptions: boolean = false,
  ): Promise<PackagesByCategoryResponseDto> {
    const supabase = this.supabaseService.getClient();

    // Use the SQL function to get packages with availability
    const { data: packages, error: packageError } = await supabase.rpc(
      'get_packages_by_category_with_availability',
      {
        p_currency_id: currencyId,
        p_city_id: cityId,
        p_venue_id: venueId,
        p_category_id: null, // Get all categories
      },
    );

    if (packageError) {
      this.logger.error(
        `Error fetching packages: ${packageError.message}`,
        packageError.stack,
      );
      throw new InternalServerErrorException('Failed to retrieve packages.');
    }

    // Organize packages by category
    const packagesByCategory = this.organizePackagesByCategory(packages);

    // Fetch options if requested
    if (includeOptions && packages.length > 0) {
      await this.addOptionsToPackages(packagesByCategory, currencyId, venueId);
    }

    return {
      categories: packagesByCategory,
    };
  }

  /**
   * Organize packages by category
   * @param packages - The packages to organize
   * @returns Packages organized by category
   */
  private organizePackagesByCategory(
    packages: any[],
  ): CategoryWithPackagesDto[] {
    // Group packages by category
    const categoryMap = new Map<string, CategoryWithPackagesDto>();

    packages.forEach(pkg => {
      const categoryId = pkg.category_id || 'uncategorized';

      if (!categoryMap.has(categoryId)) {
        categoryMap.set(categoryId, {
          id: categoryId,
          name: pkg.category_name || 'Uncategorized',
          display_order: pkg.category_display_order || 9999,
          packages: [],
        });
      }

      // Ensure the category exists in the map before accessing it
      const category = categoryMap.get(categoryId);
      if (category) {
        category.packages.push({
          id: pkg.id,
          name: pkg.name,
          description: pkg.description,
          quantity_basis: pkg.quantity_basis,
          price: pkg.price,
          unit_base_cost: pkg.unit_base_cost,
        } as CategoryPackageDto);
      }
    });

    // Convert map to array and sort by display_order
    return Array.from(categoryMap.values()).sort(
      (a, b) => a.display_order - b.display_order,
    );
  }

  /**
   * Add options to packages
   * @param categories - The categories with packages
   * @param currencyId - The currency ID
   * @param venueId - The venue ID (optional)
   */
  private async addOptionsToPackages(
    categories: CategoryWithPackagesDto[],
    currencyId: string,
    venueId?: string,
  ): Promise<void> {
    // Get all package IDs
    const packageIds = categories.flatMap(cat =>
      cat.packages.map(pkg => pkg.id),
    );

    if (packageIds.length === 0) {
      return;
    }

    // Get options for all packages
    const options = await this.getBatchPackageOptions(
      packageIds,
      currencyId,
      venueId,
    );

    // Add options to packages
    categories.forEach(category => {
      category.packages.forEach(pkg => {
        pkg.options = options.options[pkg.id] || [];
      });
    });
  }

  /**
   * Get options for multiple packages in a single request
   * @param packageIds - The package IDs
   * @param currencyId - The currency ID
   * @param venueId - The venue ID (optional)
   * @returns Options for each package
   */
  async getBatchPackageOptions(
    packageIds: string[],
    currencyId: string,
    venueId?: string,
  ): Promise<BatchPackageOptionsResponseDto> {
    this.logger.log(
      `Getting batch options for ${packageIds.length} packages, currency ${currencyId}, venue ${venueId || 'any'}`,
    );

    // Generate a cache key
    const cacheKey = `batch-package-options:${packageIds.join(',')}:${currencyId}:${venueId || 'all'}`;

    // Try to get from cache first
    return this.cacheService.getOrSet(
      cacheKey,
      () => this.fetchBatchPackageOptions(packageIds, currencyId, venueId),
      3600, // Cache for 1 hour
    );
  }

  /**
   * Fetch options for multiple packages from the database
   * @param packageIds - The package IDs
   * @param currencyId - The currency ID
   * @param venueId - The venue ID (optional)
   * @returns Options for each package
   */
  private async fetchBatchPackageOptions(
    packageIds: string[],
    currencyId: string,
    venueId?: string,
  ): Promise<BatchPackageOptionsResponseDto> {
    const supabase = this.supabaseService.getClient();

    // Use the SQL function to get options for multiple packages
    const { data: options, error: optionsError } = await supabase.rpc(
      'get_batch_package_options',
      {
        p_package_ids: packageIds,
        p_currency_id: currencyId,
        p_venue_id: venueId,
      },
    );

    if (optionsError) {
      this.logger.error(
        `Error fetching batch options: ${optionsError.message}`,
        optionsError.stack,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve package options.',
      );
    }

    // Organize options by package ID
    const optionsByPackage: Record<string, BatchPackageOptionDto[]> = {};

    options.forEach(option => {
      if (!optionsByPackage[option.package_id]) {
        optionsByPackage[option.package_id] = [];
      }

      optionsByPackage[option.package_id].push({
        id: option.option_id,
        option_name: option.option_name,
        description: option.description,
        price_adjustment: option.price_adjustment,
        cost_adjustment: option.cost_adjustment,
        is_default_for_package: option.is_default_for_package,
        is_required: option.is_required,
      } as BatchPackageOptionDto);
    });

    return {
      options: optionsByPackage,
    };
  }
}
