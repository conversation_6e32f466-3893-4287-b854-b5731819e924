import { Module } from '@nestjs/common';
import { CalculationsService } from './calculations.service';
import { CalculationsController } from './calculations.controller';
import { CalculationsPackagesController } from './calculations-packages.controller';
import { AuthModule } from '../auth/auth.module'; // May need for guards
import { CalculationItemsModule } from '../calculation-items/calculation-items.module'; // Import the new module
import { PackagesModule } from '../packages/packages.module'; // Import for package-related functionality
// Import new services
import { CalculationLogicService } from './calculation-logic.service';
import { CalculationTemplateService } from './calculation-template.service';
// Import specialized services
import {
  CalculationCrudService,
  CalculationVenueService,
  CalculationValidationService,
  CalculationTransformationService,
  CalculationStatusService,
} from './services';

@Module({
  imports: [
    AuthModule, // Import if needed for injecting AuthService or using Passport
    CalculationItemsModule, // Add the new module here
    PackagesModule, // Import for package-related functionality
  ],
  controllers: [CalculationsController, CalculationsPackagesController],
  providers: [
    CalculationsService,
    CalculationLogicService, // Add new service
    CalculationTemplateService, // Add new service
    // Specialized services for better separation of concerns
    CalculationCrudService,
    CalculationVenueService,
    CalculationValidationService,
    CalculationTransformationService,
    CalculationStatusService,
    // CalculationItemsService is provided by CalculationItemsModule
  ],
  exports: [
    CalculationsService,
    CalculationLogicService, // Export if needed by other modules
    CalculationTemplateService, // Export if needed by other modules
    // Export specialized services if needed by other modules
    CalculationCrudService,
    CalculationVenueService,
    CalculationValidationService,
    CalculationTransformationService,
    CalculationStatusService,
  ],
})
export class CalculationsModule {}
