import { CalculationStatus } from '../enums/calculation-status.enum';
import { ApiProperty } from '@nestjs/swagger';
import { VenueReferenceDto } from '../../venues/dto/venue-reference.dto';

// --- Nested DTOs for related entities ---

export class ClientSummaryDto {
  id: string;
  client_name: string;
  contact_person?: string | null;
  email?: string | null;
}

export class EventSummaryDto {
  id: string;
  event_name: string;
  event_start_datetime?: string | null;
  event_end_datetime?: string | null;
}

export class CurrencySummaryDto {
  id: string;
  code: string;
}

export class CalculationLineItemOptionDto {
  id: string; // option_id from package_options
  option_name_snapshot: string; // Need to retrieve/store this
  price_adjustment_snapshot: number;
  // cost_adjustment_snapshot: number; // If tracking cost
}

// Represents a single package-based line item within a calculation
export class CalculationLineItemDto {
  id: string;
  package_id: string | null;
  item_name_snapshot: string;
  option_summary_snapshot: string | null;
  item_quantity: number;
  duration_days: number; // Maps to item_quantity_basis in database
  unit_base_price: number; // Changed from price
  options_total_adjustment: number;
  calculated_line_total: number;
  notes: string | null;
  unit_base_cost_snapshot: number;
  options_total_cost_snapshot: number;
  calculated_line_cost: number;
  options: CalculationLineItemOptionDto[]; // Array of selected options
}

// Represents a single custom line item within a calculation
export class CalculationCustomItemDto {
  @ApiProperty({
    type: String,
    format: 'uuid',
  })
  id: string;
  @ApiProperty({
    type: String,
    description: 'Item name',
  })
  item_name: string;
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Item description',
  })
  description: string | null;
  @ApiProperty({
    type: Number,
    description: 'Item quantity',
  })
  quantity: number;
  @ApiProperty({
    type: Number,
    description: 'Item unit price',
  })
  unit_price: number;
  @ApiProperty({
    type: Number,
    description: 'Item unit cost',
  })
  unit_cost: number;
  // Note: currency is same as parent calculation
}

// Represents a basic City reference
export class CityReferenceDto {
  @ApiProperty({
    type: String,
    format: 'uuid',
  })
  id: string;
  @ApiProperty({
    type: String,
    description: 'City name',
  })
  name: string;
}

// --- Main Detail DTO ---

export class CalculationDetailDto {
  @ApiProperty({
    type: String,
    format: 'uuid',
  })
  id: string;
  @ApiProperty({
    type: String,
    description: 'Calculation name',
  })
  name: string;
  @ApiProperty({
    type: String,
    enum: CalculationStatus,
    description: 'Calculation status',
  })
  status: CalculationStatus;
  @ApiProperty({
    type: String,
    format: 'uuid',
    nullable: true,
    description: 'Event type ID (foreign key reference)',
  })
  event_type_id: string | null;
  @ApiProperty({
    type: Number,
    nullable: true,
    description: 'Number of attendees',
  })
  attendees: number | null;
  @ApiProperty({
    type: String,
    nullable: true,
    format: 'date',
    description: 'Event start date',
  })
  event_start_date: string | null;
  @ApiProperty({
    type: String,
    nullable: true,
    format: 'date',
    description: 'Event end date',
  })
  event_end_date: string | null;
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Calculation notes',
  })
  notes: string | null;
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'Version notes',
  })
  version_notes: string | null;
  @ApiProperty({
    type: String,
    format: 'date-time',
    description: 'Created at',
  })
  created_at: string;
  @ApiProperty({
    type: String,
    format: 'date-time',
    description: 'Updated at',
  })
  updated_at: string;
  @ApiProperty({
    type: String,
    format: 'uuid',
    description: 'Created by user ID',
  })
  created_by: string; // User ID

  // Related Entities
  @ApiProperty({
    type: () => CurrencySummaryDto,
    nullable: true,
    description: 'Currency details',
  })
  currency: CurrencySummaryDto | null;
  @ApiProperty({
    type: () => CityReferenceDto,
    nullable: true,
    description: 'City details',
  })
  city: CityReferenceDto | null;
  @ApiProperty({
    type: () => ClientSummaryDto,
    nullable: true,
    description: 'Client details',
  })
  client: ClientSummaryDto | null;
  @ApiProperty({
    type: () => EventSummaryDto,
    nullable: true,
    description: 'Event details',
  })
  event: EventSummaryDto | null;

  @ApiProperty({
    type: () => VenueReferenceDto,
    isArray: true,
    description: 'Venues associated with this calculation',
  })
  venues: VenueReferenceDto[];

  // Line Items
  @ApiProperty({
    type: () => CalculationLineItemDto,
    isArray: true,
    description: 'Line items',
  })
  line_items: CalculationLineItemDto[];
  @ApiProperty({
    type: () => CalculationCustomItemDto,
    isArray: true,
    description: 'Custom items',
  })
  custom_items: CalculationCustomItemDto[];

  // Add Totals directly based on schema
  @ApiProperty({
    type: Number,
    description: 'Subtotal before taxes and discounts',
  })
  subtotal: number;

  @ApiProperty({
    type: () => TaxDetailItemDto,
    isArray: true,
    nullable: true,
    description: 'Applied taxes details (JSONB)',
  })
  taxes: TaxDetailItemDto[] | null;

  @ApiProperty({
    type: () => DiscountDetailDto,
    nullable: true,
    description: 'Applied discount details (JSONB)',
  })
  discount: DiscountDetailDto | null;

  @ApiProperty({
    type: Number,
    description: 'Grand total after taxes and discounts',
  })
  total: number;

  @ApiProperty({
    type: Number,
    description: 'Total estimated cost of goods/services',
  })
  total_cost: number;

  @ApiProperty({
    type: Number,
    description: 'Estimated profit (total - total_cost)',
  })
  estimated_profit: number;
}

// Helper DTO for Tax details
export class TaxDetailItemDto {
  @ApiProperty({
    required: false,
    nullable: true,
  })
  name?: string | null;

  @ApiProperty({
    required: false,
    nullable: true,
  })
  rate?: number | null;

  @ApiProperty({
    required: false,
    nullable: true,
  })
  amount?: number | null;
}

// Helper DTO for Discount details
export class DiscountDetailDto {
  @ApiProperty({
    required: false,
    nullable: true,
  })
  name?: string | null;

  @ApiProperty({
    required: false,
    nullable: true,
  })
  amount?: number | null;

  @ApiProperty({
    required: false,
    nullable: true,
  })
  description?: string | null;

  @ApiProperty({
    required: false,
    nullable: true,
  })
  percentage?: number | null;
}
