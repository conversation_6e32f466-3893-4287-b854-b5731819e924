import { ApiProperty } from '@nestjs/swagger';

// DTO for representing a package variation in the catalogue browsing response
export class PackageVariationDto {
  @ApiProperty({ type: String, format: 'uuid' })
  package_id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ nullable: true })
  description: string | null;

  @ApiProperty({ type: String, format: 'uuid' })
  category_id: string;

  @ApiProperty({ enum: ['attendees', 'fixed', 'days', 'attendees_days'] })
  quantity_basis: 'attendees' | 'fixed' | 'days' | 'attendees_days';

  @ApiProperty({ description: 'Base price for the specified currency' })
  price: number;

  @ApiProperty({ description: 'Base cost for the specified currency' })
  unit_base_cost: number;

  @ApiProperty({
    description:
      'Indicates if the package is available in the queried city (if city filter applied)',
  })
  is_available_in_city: boolean; // True if no city filter or available in the filtered city

  @ApiProperty({
    description:
      'Indicates if the package is available in the queried venue (if venue filter applied)',
  })
  is_available_in_venue: boolean; // True if no venue filter or available in the filtered venue

  @ApiProperty({
    description:
      'Indicates if this package conflicts with currently selected items (if selection provided)',
  })
  conflicts_with_selection: boolean;
}
