import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);

  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  /**
   * Get a value from cache
   * @param key - The cache key
   * @returns The cached value or null if not found
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      return await this.cacheManager.get<T>(key);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error getting cache key ${key}: ${errorMessage}`,
        errorStack,
      );
      return null;
    }
  }

  /**
   * Set a value in cache
   * @param key - The cache key
   * @param value - The value to cache
   * @param ttl - Time to live in seconds (optional)
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      await this.cacheManager.set(key, value, ttl ? ttl : undefined);
      this.logger.verbose(
        `Cache set for key: ${key}${ttl ? ` with TTL: ${ttl}s` : ''}`,
      );
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error setting cache key ${key}: ${errorMessage}`,
        errorStack,
      );
    }
  }

  /**
   * Delete a value from cache
   * @param key - The cache key to delete
   */
  async delete(key: string): Promise<void> {
    try {
      await this.cacheManager.del(key);
      this.logger.verbose(`Cache deleted for key: ${key}`);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error deleting cache key ${key}: ${errorMessage}`,
        errorStack,
      );
    }
  }

  /**
   * Clear the entire cache
   */
  async clear(): Promise<void> {
    try {
      await this.cacheManager.clear();
      this.logger.log('Cache cleared');
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error clearing cache: ${errorMessage}`, errorStack);
    }
  }

  /**
   * Get or set cache value (convenience method)
   * @param key - The cache key
   * @param factory - Function to generate value if not in cache
   * @param ttl - Time to live in seconds (optional)
   * @returns The cached or newly generated value
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number,
  ): Promise<T> {
    const cachedValue = await this.get<T>(key);

    if (cachedValue !== null && cachedValue !== undefined) {
      this.logger.debug(`Cache hit for key: ${key}`);
      return cachedValue;
    }

    this.logger.debug(`Cache miss for key: ${key}, fetching data...`);
    const value = await factory();
    await this.set(key, value, ttl);
    return value;
  }
}
