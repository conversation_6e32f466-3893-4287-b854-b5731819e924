/**
 * Hook for managing package form state
 */
import { useState, useCallback, useMemo } from "react";
import { PackageFormState, QuantityBasisEnum } from "@/types/calculation";
import { calculatePackagePrice } from "../../utils/calculationUtils";

/**
 * Enhanced hook for managing package form state with cleanup mechanisms
 * Handles quantity, item_quantity_basis, options, and provides cleanup functions
 * for maintaining state consistency when line items are deleted.
 *
 * @returns State and functions for managing package forms
 */
export function usePackageForms() {
  const [packageForms, setPackageForms] = useState<PackageFormState>({});

  // Handle quantity changes (memoized)
  const handleQuantityChange = useCallback(
    (packageId: string, value: number) => {
      setPackageForms((prev) => ({
        ...prev,
        [packageId]: {
          ...(prev[packageId] || {
            quantity: 1,
            days: 1,
            item_quantity_basis: 1,
            selectedOptions: [],
          }),
          quantity: Math.max(1, value),
        },
      }));
    },
    []
  );

  // Handle item_quantity_basis changes (second input field) (memoized)
  const handleItemQuantityBasisChange = useCallback(
    (packageId: string, value: number) => {
      setPackageForms((prev) => ({
        ...prev,
        [packageId]: {
          ...(prev[packageId] || {
            quantity: 1,
            days: 1,
            item_quantity_basis: 1,
            selectedOptions: [],
          }),
          // Update both days (for backward compatibility) and item_quantity_basis
          days: Math.max(1, value),
          item_quantity_basis: Math.max(1, value),
        },
      }));
    },
    []
  );

  // handleDaysChange function has been removed in favor of handleItemQuantityBasisChange

  // Handle option toggling (memoized)
  const handleOptionToggle = useCallback(
    (packageId: string, optionId: string, isSelected: boolean) => {
      setPackageForms((prev) => {
        const currentPackage = prev[packageId] || {
          quantity: 1,
          days: 1,
          item_quantity_basis: 1,
          selectedOptions: [],
        };
        const currentSelectedOptions = currentPackage.selectedOptions || [];

        let newSelectedOptions: string[];
        if (isSelected) {
          // Add option if it's not already selected
          newSelectedOptions = [...currentSelectedOptions, optionId];
        } else {
          // Remove option if it's selected
          newSelectedOptions = currentSelectedOptions.filter(
            (id) => id !== optionId
          );
        }

        return {
          ...prev,
          [packageId]: {
            ...currentPackage,
            selectedOptions: newSelectedOptions,
          },
        };
      });
    },
    []
  );

  // Calculate total price for a package (memoized)
  const calculatePackageTotalPrice = useCallback(
    (
      packageId: string,
      basePrice: string | number,
      quantityBasis: QuantityBasisEnum | undefined,
      packagesByCategory: any[]
    ) => {
      const {
        quantity = 1,
        item_quantity_basis = 1,
        selectedOptions = [],
      } = packageForms[packageId] || {};

      // Find the package in packagesByCategory to get its options
      let packageOptions: any[] = [];
      let currencySymbol = "Rp";

      if (packagesByCategory) {
        // Find the package and its options
        for (const category of packagesByCategory) {
          const pkg = category.packages.find((p: any) => p.id === packageId);
          if (pkg) {
            if (pkg.options) {
              packageOptions = pkg.options;
            }
            if (pkg.currencySymbol) {
              currencySymbol = pkg.currencySymbol;
            }
            break;
          }
        }
      }

      // Use the utility function to calculate the total price
      const selectedPackageOptions = packageOptions.filter((opt) =>
        selectedOptions.includes(opt.id)
      );
      const { totalPrice } = calculatePackagePrice(
        basePrice,
        quantity,
        item_quantity_basis,
        quantityBasis,
        selectedPackageOptions,
        currencySymbol
      );

      return totalPrice;
    },
    [packageForms]
  );

  // Cleanup function for when a specific package's line item is removed
  const cleanupPackageForm = useCallback((packageId: string) => {
    setPackageForms((prev) => {
      const { [packageId]: removed, ...rest } = prev;
      return rest;
    });
  }, []);

  // Bulk cleanup for multiple packages
  const cleanupMultiplePackageForms = useCallback((packageIds: string[]) => {
    setPackageForms((prev) => {
      const newForms = { ...prev };
      packageIds.forEach((id) => delete newForms[id]);
      return newForms;
    });
  }, []);

  // Reset all package forms (useful for major state changes)
  const resetAllPackageForms = useCallback(() => {
    setPackageForms({});
  }, []);

  // Get package form data for a specific package
  const getPackageFormData = useCallback(
    (packageId: string) => {
      return (
        packageForms[packageId] || {
          quantity: 1,
          days: 1,
          item_quantity_basis: 1,
          selectedOptions: [],
        }
      );
    },
    [packageForms]
  );

  return useMemo(
    () => ({
      packageForms,
      handleQuantityChange,
      handleItemQuantityBasisChange,
      handleOptionToggle,
      calculatePackageTotalPrice,
      cleanupPackageForm,
      cleanupMultiplePackageForms,
      resetAllPackageForms,
      getPackageFormData,
    }),
    [
      packageForms,
      handleQuantityChange,
      handleItemQuantityBasisChange,
      handleOptionToggle,
      calculatePackageTotalPrice,
      cleanupPackageForm,
      cleanupMultiplePackageForms,
      resetAllPackageForms,
      getPackageFormData,
    ]
  );
}
