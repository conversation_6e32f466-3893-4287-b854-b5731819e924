/**
 * Hook for managing calculation detail page actions
 * Centralizes all action handlers for the calculation detail page
 */
import { useNavigate } from "react-router-dom";
import { useCallback, useMemo } from "react";
import { deleteCalculation } from "../../../services/calculations";
import { debug } from "@/lib/debugUtils";

interface UseCalculationActionsProps {
  calculationId: string;
  saveTaxesAndDiscount: (
    newStatus?: "draft" | "completed" | "canceled"
  ) => Promise<boolean>;
}

export const useCalculationActions = ({
  calculationId,
  saveTaxesAndDiscount,
}: UseCalculationActionsProps) => {
  const navigate = useNavigate();

  // Handler for status change
  const handleStatusChange = useCallback(
    async (newStatus: "draft" | "completed" | "canceled"): Promise<void> => {
      try {
        // Use our hook to save taxes and discount with the new status
        const success = await saveTaxesAndDiscount(newStatus);

        if (!success) {
          throw new Error(`Failed to change status to ${newStatus}`);
        }

        // If the status is changing to 'draft', we'll navigate back to the calculations list
        // (This is handled in the CalculationFinancialSummary component)
      } catch (error) {
        debug(`Error changing status to ${newStatus}:`, error);
        throw error;
      }
    },
    [saveTaxesAndDiscount]
  );

  // Handler for calculation deletion
  const handleDelete = useCallback(async (): Promise<void> => {
    try {
      await deleteCalculation(calculationId);

      // Navigate back to calculations list after successful deletion
      navigate("/calculations");
    } catch (error) {
      debug("Error deleting calculation:", error);
      throw error;
    }
  }, [calculationId, navigate]);

  // Handler for navigation back to calculations list
  const handleNavigateToList = useCallback(() => {
    navigate("/calculations");
  }, [navigate]);

  // Handler for navigation back with button
  const handleNavigateBack = useCallback(() => {
    navigate("/calculations");
  }, [navigate]);

  return useMemo(
    () => ({
      handleStatusChange,
      handleDelete,
      handleNavigateToList,
      handleNavigateBack,
    }),
    [handleStatusChange, handleDelete, handleNavigateToList, handleNavigateBack]
  );
};
