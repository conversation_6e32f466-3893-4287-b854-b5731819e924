import { Module, forwardRef } from '@nestjs/common';
import { ExportsService } from './exports.service';
import { ExportsController } from './exports.controller';
import { SupabaseModule } from '../../core/supabase/supabase.module';
import { CalculationsModule } from '../calculations/calculations.module';
import { BullModule } from '@nestjs/bullmq';
import { ExportsProcessor } from './exports.processor';
import { CsvExportProcessor } from './processors/csv-export.processor';
import { PdfExportProcessor } from './processors/pdf-export.processor';
import { XlsxExportProcessor } from './processors/xlsx-export.processor';
import { ExportGenerationService } from './services/export-generation.service';
import { ExportStorageService } from './services/export-storage.service';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    AuthModule,
    SupabaseModule,
    forwardRef(() => CalculationsModule),
    BullModule.registerQueue({ name: 'csv-exports' }),
    BullModule.registerQueue({ name: 'pdf-exports' }),
    BullModule.registerQueue({ name: 'xlsx-exports' }),
  ],
  controllers: [ExportsController],
  providers: [
    ExportsService,
    // Keep the old processor for backward compatibility during transition
    ExportsProcessor,
    // Add new format-specific processors
    CsvExportProcessor,
    PdfExportProcessor,
    XlsxExportProcessor,
    ExportGenerationService,
    ExportStorageService,
  ],
  exports: [ExportsService],
})
export class ExportsModule {}
