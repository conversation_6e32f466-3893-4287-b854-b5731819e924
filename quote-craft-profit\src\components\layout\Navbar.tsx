import React, { useMemo } from "react";
import { Link, useLocation } from "react-router-dom";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from "@/contexts/useAuth";
import NotificationCenter from "@/components/notifications/NotificationCenter";
import {
  LayoutDashboard,
  Calculator,
  Calendar,
  Users,
  FileText,
  ChevronDown,
  Settings,
  BarChart3,
  Package,
  Building2,
  Map,
  Layers,
  ShieldCheck,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { ThemeToggleSimple } from "@/components/ui/theme-toggle";

const Navbar: React.FC = () => {
  const location = useLocation();
  const { user, isAdmin, signOut } = useAuth();

  // Get user information for display
  const userInitial = user?.email ? user.email[0].toUpperCase() : "U";
  const userFullName = user?.user_metadata?.full_name || user?.email || "User";

  // Improved active path detection - memoize to prevent recreation on each render
  const isActive = useMemo(() => {
    return (path: string) => {
      return (
        location.pathname === path || location.pathname.startsWith(`${path}/`)
      );
    };
  }, [location.pathname]);

  return (
    <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 sticky top-0 z-10 shadow-sm">
      <div className="container mx-auto px-4">
        <div className="h-16 flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex md:hidden"></div>
            <Link to="/" className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-eventcost-primary text-white flex items-center justify-center rounded">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <span className="font-bold text-xl tracking-tight text-gray-900 dark:text-white">
                EventCost Pro
              </span>
            </Link>

            {/* Main Navigation - Desktop */}
            <nav className="ml-10 hidden md:flex items-center space-x-1">
              {/* Dashboard */}
              <Link
                to="/"
                className={`px-3 py-2 rounded-md ${
                  isActive("/")
                    ? "text-eventcost-primary font-medium bg-blue-50 dark:bg-blue-900/20"
                    : "text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                } transition-colors flex items-center space-x-1`}
              >
                <LayoutDashboard className="h-4 w-4" />
                <span>Dashboard</span>
              </Link>

              {/* Dashboard V2 */}
              <Link
                to="/dashboard-v2"
                className={`px-3 py-2 rounded-md ${
                  isActive("/dashboard-v2")
                    ? "text-eventcost-primary font-medium bg-blue-50 dark:bg-blue-900/20"
                    : "text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                } transition-colors flex items-center space-x-1`}
              >
                <LayoutDashboard className="h-4 w-4" />
                <span>Dashboard V2</span>
              </Link>

              {/* Calculations */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant={isActive("/calculations") ? "secondary" : "ghost"}
                    className={`px-3 py-2 h-auto ${
                      isActive("/calculations")
                        ? "text-eventcost-primary"
                        : "text-gray-500 dark:text-gray-400"
                    }`}
                  >
                    <Calculator className="h-4 w-4 mr-1" />
                    <span>Calculations</span>
                    <ChevronDown className="h-4 w-4 ml-1" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-56">
                  <DropdownMenuItem asChild>
                    <Link to="/calculations" className="flex items-center">
                      <BarChart3 className="h-4 w-4 mr-2" />
                      <span>All Calculations</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/calculations/new" className="flex items-center">
                      <Calculator className="h-4 w-4 mr-2" />
                      <span>New Calculation</span>
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Events */}
              <Link
                to="/events"
                className={`px-3 py-2 rounded-md ${
                  isActive("/events")
                    ? "text-eventcost-primary font-medium bg-blue-50 dark:bg-blue-900/20"
                    : "text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                } transition-colors flex items-center space-x-1`}
              >
                <Calendar className="h-4 w-4" />
                <span>Events</span>
              </Link>

              {/* Clients */}
              <Link
                to="/clients"
                className={`px-3 py-2 rounded-md ${
                  isActive("/clients")
                    ? "text-eventcost-primary font-medium bg-blue-50 dark:bg-blue-900/20"
                    : "text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                } transition-colors flex items-center space-x-1`}
              >
                <Users className="h-4 w-4" />
                <span>Clients</span>
              </Link>

              {/* Templates */}
              <Link
                to="/templates"
                className={`px-3 py-2 rounded-md ${
                  isActive("/templates")
                    ? "text-eventcost-primary font-medium bg-blue-50 dark:bg-blue-900/20"
                    : "text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                } transition-colors flex items-center space-x-1`}
              >
                <FileText className="h-4 w-4" />
                <span>Templates</span>
              </Link>

              {/* Admin Menu - Only show if user is admin */}
              {isAdmin && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant={isActive("/admin") ? "secondary" : "ghost"}
                      className={`px-3 py-2 h-auto ${
                        isActive("/admin")
                          ? "text-eventcost-primary"
                          : "text-gray-500 dark:text-gray-400"
                      }`}
                    >
                      <ShieldCheck className="h-4 w-4 mr-1" />
                      <span>Admin</span>
                      <ChevronDown className="h-4 w-4 ml-1" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-56">
                    <DropdownMenuItem asChild>
                      <Link to="/admin" className="flex items-center">
                        <LayoutDashboard className="h-4 w-4 mr-2" />
                        <span>Admin Dashboard</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="/admin/users" className="flex items-center">
                        <Users className="h-4 w-4 mr-2" />
                        <span>Users</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSub>
                      <DropdownMenuSubTrigger>
                        <Package className="h-4 w-4 mr-2" />
                        <span>Catalogue</span>
                      </DropdownMenuSubTrigger>
                      <DropdownMenuSubContent className="w-56">
                        <DropdownMenuItem asChild>
                          <Link to="/admin/packages">Packages</Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link to="/admin/categories">Categories</Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link to="/admin/divisions">Divisions</Link>
                        </DropdownMenuItem>
                      </DropdownMenuSubContent>
                    </DropdownMenuSub>
                    <DropdownMenuItem asChild>
                      <Link to="/admin/cities" className="flex items-center">
                        <Map className="h-4 w-4 mr-2" />
                        <span>Cities</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="/admin/venues" className="flex items-center">
                        <Building2 className="h-4 w-4 mr-2" />
                        <span>Venues</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link
                        to="/admin/event-types"
                        className="flex items-center"
                      >
                        <Calendar className="h-4 w-4 mr-2" />
                        <span>Event Types</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="/admin/templates" className="flex items-center">
                        <Layers className="h-4 w-4 mr-2" />
                        <span>Templates</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="/admin/settings" className="flex items-center">
                        <Settings className="h-4 w-4 mr-2" />
                        <span>Settings</span>
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </nav>
          </div>

          <div className="flex items-center">
            <NotificationCenter />

            {/* Theme Toggle */}
            <div className="ml-2">
              <ThemeToggleSimple />
            </div>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger className="ml-4 flex items-center space-x-2 focus:outline-none">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={null} />
                  <AvatarFallback>{userInitial}</AvatarFallback>
                </Avatar>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300 hidden md:block">
                  {userFullName}
                </span>
                <ChevronDown className="h-4 w-4 text-gray-400 dark:text-gray-500" />
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  <DropdownMenuItem asChild>
                    <Link to="/profile/settings" className="flex items-center">
                      <Users className="h-4 w-4 mr-2" />
                      <span>Profile & Settings</span>
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuGroup>

                {/* Admin Section - Only show if user is admin */}
                {isAdmin && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Administration</DropdownMenuLabel>
                    <DropdownMenuItem asChild>
                      <Link to="/admin" className="flex items-center">
                        <ShieldCheck className="h-4 w-4 mr-2" />
                        <span>Admin Dashboard</span>
                      </Link>
                    </DropdownMenuItem>
                  </>
                )}

                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => signOut()}
                  className="flex items-center text-red-600"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                    />
                  </svg>
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
