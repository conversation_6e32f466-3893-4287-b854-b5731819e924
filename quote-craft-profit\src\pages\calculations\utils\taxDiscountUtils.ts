/**
 * Tax and discount calculation utilities
 */

// Tax and Discount interfaces
export interface Tax {
  id?: string;
  name: string;
  rate: number; // Percentage (e.g., 10 for 10%)
  amount?: number; // Pre-calculated amount
}

export interface Discount {
  id?: string;
  name: string;
  type: 'percentage' | 'fixed';
  value: number; // Percentage or fixed amount
  amount?: number; // Pre-calculated amount
}

/**
 * Calculate taxes based on subtotal and tax rates
 * @param subtotal - The subtotal to calculate taxes on
 * @param taxes - Array of tax objects with rates
 * @returns Array of tax objects with calculated amounts
 */
export const calculateTaxes = (subtotal: number, taxes: Tax[]): Tax[] => {
  if (!taxes || !Array.isArray(taxes) || taxes.length === 0) {
    return [];
  }

  return taxes.map((tax) => {
    const amount = (subtotal * tax.rate) / 100;
    return {
      ...tax,
      amount,
    };
  });
};

/**
 * Calculate discount based on subtotal and discount information
 * @param subtotal - The subtotal to calculate discount on
 * @param discount - Discount object with type and value
 * @returns Discount object with calculated amount
 */
export const calculateDiscount = (subtotal: number, discount: Discount | null): Discount => {
  if (!discount) {
    return {
      name: 'No Discount',
      type: 'fixed',
      value: 0,
      amount: 0,
    };
  }

  let amount = 0;
  if (discount.type === 'percentage') {
    amount = (subtotal * discount.value) / 100;
  } else {
    amount = discount.value;
  }

  return {
    ...discount,
    amount,
  };
};

/**
 * Calculate the final total after applying taxes and discounts
 * @param subtotal - The subtotal before taxes and discounts
 * @param taxes - Array of tax objects
 * @param discount - Discount object
 * @returns Object with subtotal, taxes total, discount amount, and final total
 */
export const calculateFinalTotal = (
  subtotal: number,
  taxes: Tax[],
  discount: Discount | null,
): {
  subtotal: number;
  taxesTotal: number;
  discountAmount: number;
  total: number;
  calculatedTaxes: { tax: Tax; amount: number }[];
} => {
  // Calculate taxes
  const calculatedTaxes = calculateTaxes(subtotal, taxes);
  const taxesTotal = calculatedTaxes.reduce((sum, tax) => sum + (tax.amount || 0), 0);

  // Calculate discount
  const calculatedDiscount = calculateDiscount(subtotal, discount);
  const discountAmount = calculatedDiscount.amount || 0;

  // Calculate final total
  const total = subtotal + taxesTotal - discountAmount;

  return {
    subtotal,
    taxesTotal,
    discountAmount,
    total,
    calculatedTaxes: calculatedTaxes.map((tax) => ({ tax, amount: tax.amount || 0 })),
  };
};
