/**
 * Profile Settings Page
 *
 * Main page for user profile and preferences management
 */

import React, { useState, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { User, Settings, Clock, Bell, Shield, ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/useAuth";
import { useUserPreferences } from "@/hooks/useUserPreferences";
import { getUserProfile } from "@/services/shared/users";
import { toast } from "sonner";
import TimezoneSettings from "./components/TimezoneSettings";
import {
  ProfilePictureSection,
  PersonalInfoSection,
  PasswordChangeSection,
} from "./components";

// Define the profile data type
interface ProfileData {
  full_name: string;
  username: string;
  role_name: string;
  phone_number: string;
  address: string;
  city: string;
  company_name: string;
  profile_picture_url: string | null;
  _timestamp?: number; // Optional timestamp for forcing re-renders
}

const ProfileSettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { preferences } = useUserPreferences();
  const [isLoading, setIsLoading] = useState(false);
  const [formVersion, setFormVersion] = useState(0);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);

  // Fetch user profile data when component mounts
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) return;
      setIsLoading(true);
      try {
        const data = await getUserProfile();

        if (data) {
          const newProfileData: ProfileData = {
            full_name: data.fullName || "",
            username: data.username || "",
            role_name: data.role || "user",
            phone_number: data.phoneNumber || "",
            address: data.address || "",
            city: data.city || "",
            company_name: data.companyName || "",
            profile_picture_url: data.profilePictureUrl,
            _timestamp: Date.now(),
          };

          setProfileData(newProfileData);
        }
      } catch (error) {
        console.error("Error fetching profile:", error);
        toast.error("Failed to load profile data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserProfile();
  }, [user]);

  // Handle profile picture update
  const handleProfilePictureUpdate = (url: string) => {
    if (profileData) {
      setProfileData({
        ...profileData,
        profile_picture_url: url,
        _timestamp: Date.now(),
      });

      // Refresh profile data after a short delay
      const refreshUserProfile = async () => {
        try {
          const data = await getUserProfile();
          if (data) {
            const refreshedProfileData: ProfileData = {
              full_name: data.fullName || "",
              username: data.username || "",
              role_name: data.role || "user",
              phone_number: data.phoneNumber || "",
              address: data.address || "",
              city: data.city || "",
              company_name: data.companyName || "",
              profile_picture_url: data.profilePictureUrl,
              _timestamp: Date.now(),
            };
            setProfileData(refreshedProfileData);
          }
        } catch (error) {
          console.error(
            "Error refreshing profile after picture update:",
            error
          );
        }
      };

      setTimeout(refreshUserProfile, 500);
    }
  };

  // Handle profile data update
  const handleProfileUpdate = (updatedData: any) => {
    setIsLoading(true);
    const newVersion = formVersion + 1;
    setFormVersion(newVersion);

    const newProfileData = {
      ...updatedData,
    };

    setProfileData(newProfileData);

    setTimeout(() => {
      setIsLoading(false);
    }, 300);
  };

  if (isLoading && !profileData) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Loading profile data...</p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(-1)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Profile Settings</h1>
              <p className="text-gray-600 dark:text-gray-400">
                Manage your account preferences and application settings
              </p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate("/profile")}
            className="flex items-center gap-2"
          >
            <User className="h-4 w-4" />
            Simple Profile View
          </Button>
        </div>

        {/* Settings Tabs */}
        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Profile
            </TabsTrigger>
            <TabsTrigger value="timezone" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Timezone
            </TabsTrigger>
            <TabsTrigger
              value="notifications"
              className="flex items-center gap-2"
            >
              <Bell className="h-4 w-4" />
              Notifications
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Security
            </TabsTrigger>
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile">
            <div className="space-y-6">
              {/* Profile Picture Section */}
              {profileData && (
                <ProfilePictureSection
                  user={user}
                  profilePictureUrl={profileData.profile_picture_url}
                  fullName={profileData.full_name}
                  onPictureUpdated={handleProfilePictureUpdate}
                />
              )}

              {/* Personal Information Section */}
              {profileData && (
                <PersonalInfoSection
                  key={`profile-info-v${formVersion}`}
                  user={user}
                  profileData={profileData}
                  onProfileUpdated={handleProfileUpdate}
                />
              )}

              {/* Password Change Section */}
              <PasswordChangeSection user={user} />
            </div>
          </TabsContent>

          {/* Timezone Settings Tab */}
          <TabsContent value="timezone">
            <TimezoneSettings />
          </TabsContent>

          {/* Notifications Tab */}
          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Notification Preferences
                </CardTitle>
                <CardDescription>
                  Control how and when you receive notifications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      Notification settings are coming soon. You'll be able to
                      control email, push, and SMS notifications.
                    </p>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">Coming Soon</Badge>
                      <span className="text-sm">Email, Push, SMS Settings</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Security Settings
                </CardTitle>
                <CardDescription>
                  Manage your account security and privacy settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      Security features are coming soon. You'll be able to
                      manage passwords, two-factor authentication, and more.
                    </p>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">Coming Soon</Badge>
                      <span className="text-sm">Password, 2FA, Privacy</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Current Preferences Summary */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Current Preferences Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="p-3 border rounded-lg">
                <Label className="text-sm text-gray-500">Timezone</Label>
                <p className="font-medium">
                  {preferences?.timezone || "Asia/Jakarta"}
                </p>
              </div>
              <div className="p-3 border rounded-lg">
                <Label className="text-sm text-gray-500">Date Format</Label>
                <p className="font-medium">
                  {preferences?.dateFormat || "dd/MM/yyyy"}
                </p>
              </div>
              <div className="p-3 border rounded-lg">
                <Label className="text-sm text-gray-500">Currency</Label>
                <p className="font-medium">{preferences?.currency || "IDR"}</p>
              </div>
              <div className="p-3 border rounded-lg">
                <Label className="text-sm text-gray-500">Language</Label>
                <p className="font-medium">
                  {preferences?.language || "English"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default ProfileSettingsPage;
