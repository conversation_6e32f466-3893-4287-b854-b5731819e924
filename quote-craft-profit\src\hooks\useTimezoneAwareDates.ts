import { useUserPreferences } from "./useUserPreferences";
import {
  formatDateForDisplay,
  formatDateForSubmission,
  formatDateTimeForSubmission,
  convertDateRangeForSubmission,
  convertDatabaseDatesToRange,
  transformSeparateDatesToDateRange,
  transformSeparateDatetimesToDateRange,
  validateDateRange,
  getDateRangeErrorMessage,
  isDateRangeComplete,
  formatDateRangeForDisplay,
} from "@/lib/timezone-utils";

/**
 * Custom hook for timezone-aware date operations
 * Provides centralized access to timezone-aware date formatting and conversion functions
 *
 * @returns Object with timezone and utility functions
 */
export const useTimezoneAwareDates = () => {
  const { timezone } = useUserPreferences();

  return {
    timezone,

    /**
     * Format a date string for display in the user's timezone
     * @param dateString - ISO date string or YYYY-MM-DD format
     * @param pattern - Optional format pattern (defaults to 'dd MMM yyyy')
     * @returns Formatted date string
     */
    formatForDisplay: (dateString: string, pattern?: string) =>
      formatDateForDisplay(dateString, timezone, pattern),

    /**
     * Format a Date object for form submission (YYYY-MM-DD format)
     * @param date - Date object to format
     * @returns YYYY-MM-DD formatted string
     */
    formatForSubmission: (date: Date) =>
      formatDateForSubmission(date, timezone),

    /**
     * Format a Date object for datetime submission (ISO format)
     * @param date - Date object to format
     * @returns ISO formatted datetime string
     */
    formatDateTimeForSubmission: (date: Date) =>
      formatDateTimeForSubmission(date, timezone),

    /**
     * Convert a date range to submission format
     * @param dateRange - Date range object with from/to dates
     * @returns Object with formatted start/end dates
     */
    convertRangeForSubmission: (dateRange: { from?: Date; to?: Date }) =>
      convertDateRangeForSubmission(dateRange, timezone),

    /**
     * Convert database date strings to DateRange object
     * @param startDate - Start date string from database
     * @param endDate - End date string from database
     * @returns DateRange object for form initialization
     */
    convertDatabaseToRange: (startDate: string, endDate: string) =>
      convertDatabaseDatesToRange(startDate, endDate, timezone),

    /**
     * Transform separate date strings to DateRange (timezone-aware)
     * @param startDate - Start date string from database
     * @param endDate - End date string from database
     * @returns DateRange object for form initialization
     */
    transformSeparateDatesToRange: (
      startDate?: string | null,
      endDate?: string | null
    ) => transformSeparateDatesToDateRange(startDate, endDate, timezone),

    /**
     * Transform separate datetime strings to DateRange (timezone-aware)
     * @param startDatetime - Start datetime string from database
     * @param endDatetime - End datetime string from database
     * @returns DateRange object for form initialization
     */
    transformSeparateDatetimesToRange: (
      startDatetime?: string | null,
      endDatetime?: string | null
    ) =>
      transformSeparateDatetimesToDateRange(
        startDatetime,
        endDatetime,
        timezone
      ),

    /**
     * Validate that a date range is valid (start date is not after end date)
     * @param dateRange - Date range object to validate
     * @returns True if valid, false otherwise
     */
    validateDateRange: (dateRange?: { from?: Date; to?: Date }) =>
      validateDateRange(dateRange),

    /**
     * Get a human-readable error message for invalid date ranges
     * @param dateRange - Date range object to check
     * @returns Error message or null if valid
     */
    getDateRangeErrorMessage: (dateRange?: { from?: Date; to?: Date }) =>
      getDateRangeErrorMessage(dateRange),

    /**
     * Check if a date range is complete (has both start and end dates)
     * @param dateRange - Date range object to check
     * @returns True if complete, false otherwise
     */
    isDateRangeComplete: (dateRange?: { from?: Date; to?: Date }) =>
      isDateRangeComplete(dateRange),

    /**
     * Format a date range for display with timezone awareness
     * @param dateRange - Date range object to format
     * @param formatPattern - Optional format pattern
     * @returns Formatted date range string
     */
    formatDateRangeForDisplay: (
      dateRange?: { from?: Date; to?: Date },
      formatPattern?: string
    ) => formatDateRangeForDisplay(dateRange, timezone, formatPattern),
  };
};

export default useTimezoneAwareDates;
