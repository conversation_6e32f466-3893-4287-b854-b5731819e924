/**
 * Reusable StatusBadge component for displaying status with consistent styling
 */
import React from "react";
import { cn } from "@/lib/utils";

export type StatusType =
  | "draft"
  | "completed"
  | "canceled"
  | "active"
  | "inactive"
  | "pending";

interface StatusBadgeProps {
  status: StatusType;
  className?: string;
  size?: "sm" | "md" | "lg";
}

/**
 * Get status styling configuration
 */
const getStatusConfig = (status: StatusType) => {
  const configs = {
    draft: {
      className:
        "bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800",
      label: "Draft",
    },
    completed: {
      className:
        "bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800",
      label: "Completed",
    },
    canceled: {
      className:
        "bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800",
      label: "Canceled",
    },
    active: {
      className:
        "bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-800",
      label: "Active",
    },
    inactive: {
      className:
        "bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-700",
      label: "Inactive",
    },
    pending: {
      className:
        "bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-300 border-orange-200 dark:border-orange-800",
      label: "Pending",
    },
  };

  return configs[status] || configs.draft;
};

/**
 * Get size styling
 */
const getSizeConfig = (size: "sm" | "md" | "lg") => {
  const configs = {
    sm: "px-2 py-1 text-xs",
    md: "px-2.5 py-1.5 text-sm",
    lg: "px-3 py-2 text-base",
  };

  return configs[size] || configs.md;
};

/**
 * StatusBadge component
 */
export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  className,
  size = "md",
}) => {
  const statusConfig = getStatusConfig(status);
  const sizeConfig = getSizeConfig(size);

  return (
    <span
      className={cn(
        "inline-flex items-center rounded-full border font-medium",
        statusConfig.className,
        sizeConfig,
        className
      )}
    >
      {statusConfig.label}
    </span>
  );
};

export default StatusBadge;
