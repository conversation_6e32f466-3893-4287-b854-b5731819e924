"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CalculationItemsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationItemsService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
let CalculationItemsService = CalculationItemsService_1 = class CalculationItemsService {
    supabaseService;
    logger = new common_1.Logger(CalculationItemsService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async getCalculationItems(calculationId) {
        this.logger.log(`Fetching line items for calculation ID: ${calculationId}`);
        const supabase = this.supabaseService.getClient();
        try {
            const { data: packageItems, error: packageError } = await supabase
                .from('calculation_line_items')
                .select(`
          id,
          calculation_id,
          package_id,
          item_name_snapshot,
          notes,
          item_quantity,
          item_quantity_basis,
          quantity_basis,
          unit_base_price,
          calculated_line_total,
          created_at,
          updated_at,
          calculation_line_item_options (
            id,
            option_id,
            price_adjustment_snapshot,
            package_options (
              option_name
            )
          )
        `)
                .eq('calculation_id', calculationId)
                .order('created_at', { ascending: true });
            if (packageError) {
                this.logger.error(`Error fetching package items: ${packageError.message}`);
                throw new common_1.InternalServerErrorException('Failed to fetch package line items');
            }
            const { data: customItems, error: customError } = await supabase
                .from('calculation_custom_items')
                .select(`
          id,
          calculation_id,
          item_name,
          description,
          item_quantity,
          item_quantity_basis,
          quantity_basis,
          unit_price,
          unit_cost,
          category_id,
          created_at,
          updated_at
        `)
                .eq('calculation_id', calculationId)
                .order('created_at', { ascending: true });
            if (customError) {
                this.logger.error(`Error fetching custom items: ${customError.message}`);
                throw new common_1.InternalServerErrorException('Failed to fetch custom line items');
            }
            const transformedPackageItems = packageItems.map(item => ({
                id: item.id,
                calculation_id: item.calculation_id,
                package_id: item.package_id,
                item_name: item.item_name_snapshot,
                item_name_snapshot: item.item_name_snapshot,
                description: null,
                notes: item.notes,
                quantity: item.item_quantity,
                item_quantity: item.item_quantity,
                item_quantity_basis: item.item_quantity_basis,
                duration_days: item.item_quantity_basis,
                quantity_basis: item.quantity_basis,
                unit_price: null,
                unit_base_price: item.unit_base_price,
                calculated_line_total: item.calculated_line_total,
                category_id: null,
                is_custom: false,
                options: item.calculation_line_item_options?.map(option => ({
                    option_id: option.option_id,
                    option_name: option.package_options?.[0]?.option_name,
                })) || [],
                created_at: item.created_at,
                updated_at: item.updated_at,
            }));
            const transformedCustomItems = customItems.map(item => ({
                id: item.id,
                calculation_id: item.calculation_id,
                package_id: null,
                item_name: item.item_name,
                item_name_snapshot: null,
                description: item.description,
                notes: null,
                quantity: item.item_quantity,
                item_quantity: item.item_quantity,
                item_quantity_basis: item.item_quantity_basis,
                duration_days: item.item_quantity_basis,
                quantity_basis: item.quantity_basis,
                unit_price: item.unit_price,
                unit_base_price: null,
                calculated_line_total: item.item_quantity * item.unit_price * item.item_quantity_basis,
                category_id: item.category_id,
                is_custom: true,
                options: [],
                created_at: item.created_at,
                updated_at: item.updated_at,
            }));
            const allItems = [...transformedPackageItems, ...transformedCustomItems];
            allItems.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
            this.logger.log(`Fetched ${allItems.length} line items for calculation ID: ${calculationId}`);
            return allItems;
        }
        catch (error) {
            this.logger.error(`Error in getCalculationItems: ${error.message}`);
            throw error;
        }
    }
    async getLineItemById(calculationId, itemId) {
        this.logger.log(`Fetching line item ID: ${itemId} for calculation ID: ${calculationId}`);
        const supabase = this.supabaseService.getClient();
        try {
            const { data: packageItem, error: packageError } = await supabase
                .from('calculation_line_items')
                .select(`
          id,
          calculation_id,
          package_id,
          item_name_snapshot,
          notes,
          item_quantity,
          item_quantity_basis,
          quantity_basis,
          unit_base_price,
          calculated_line_total,
          created_at,
          updated_at,
          calculation_line_item_options (
            id,
            option_id,
            price_adjustment_snapshot,
            package_options (
              option_name
            )
          )
        `)
                .eq('id', itemId)
                .eq('calculation_id', calculationId)
                .single();
            if (!packageError && packageItem) {
                return {
                    id: packageItem.id,
                    calculation_id: packageItem.calculation_id,
                    package_id: packageItem.package_id,
                    item_name: packageItem.item_name_snapshot,
                    item_name_snapshot: packageItem.item_name_snapshot,
                    description: null,
                    notes: packageItem.notes,
                    quantity: packageItem.item_quantity,
                    item_quantity: packageItem.item_quantity,
                    item_quantity_basis: packageItem.item_quantity_basis,
                    duration_days: packageItem.item_quantity_basis,
                    quantity_basis: packageItem.quantity_basis,
                    unit_price: null,
                    unit_base_price: packageItem.unit_base_price,
                    calculated_line_total: packageItem.calculated_line_total,
                    category_id: null,
                    is_custom: false,
                    options: packageItem.calculation_line_item_options?.map(option => ({
                        option_id: option.option_id,
                        option_name: option.package_options?.[0]?.option_name,
                    })) || [],
                    created_at: packageItem.created_at,
                    updated_at: packageItem.updated_at,
                };
            }
            const { data: customItem, error: customError } = await supabase
                .from('calculation_custom_items')
                .select(`
          id,
          calculation_id,
          item_name,
          description,
          item_quantity,
          item_quantity_basis,
          quantity_basis,
          unit_price,
          unit_cost,
          category_id,
          created_at,
          updated_at
        `)
                .eq('id', itemId)
                .eq('calculation_id', calculationId)
                .single();
            if (customError) {
                this.logger.error(`Error fetching line item: ${customError.message}`);
                throw new common_1.NotFoundException(`Line item with ID ${itemId} not found`);
            }
            if (!customItem) {
                throw new common_1.NotFoundException(`Line item with ID ${itemId} not found`);
            }
            return {
                id: customItem.id,
                calculation_id: customItem.calculation_id,
                package_id: null,
                item_name: customItem.item_name,
                item_name_snapshot: null,
                description: customItem.description,
                notes: null,
                quantity: customItem.item_quantity,
                item_quantity: customItem.item_quantity,
                item_quantity_basis: customItem.item_quantity_basis,
                duration_days: customItem.item_quantity_basis,
                quantity_basis: customItem.quantity_basis,
                unit_price: customItem.unit_price,
                unit_base_price: null,
                calculated_line_total: customItem.item_quantity * customItem.unit_price * customItem.item_quantity_basis,
                category_id: customItem.category_id,
                is_custom: true,
                options: [],
                created_at: customItem.created_at,
                updated_at: customItem.updated_at,
            };
        }
        catch (error) {
            this.logger.error(`Error in getLineItemById: ${error.message}`);
            throw error;
        }
    }
    async populateItemsFromTemplateBlueprint(calculationId, currencyId, packageSelections, user) {
        this.logger.log(`Populating items for calculation ${calculationId} (Currency: ${currencyId}). User: ${user.id}`);
        const supabase = this.supabaseService.getClient();
        for (const item of packageSelections) {
            this.logger.log(`Processing package ${item.package_id} for calc ${calculationId} via RPC.`);
            this.logger.debug(`Processing item: ${JSON.stringify(item)}`);
            try {
                const rpcParams = {
                    p_calculation_id: calculationId,
                    p_user_id: user.id,
                    p_package_id: item.package_id,
                    p_option_ids: item.option_ids ?? [],
                    p_currency_id: currencyId,
                    p_quantity_override: item.item_quantity,
                    p_duration_override: item.item_quantity_basis,
                    p_notes: `Added from template blueprint`,
                };
                this.logger.debug(`Calling RPC add_package_item_and_recalculate with params: ${JSON.stringify(rpcParams)}`);
                const rpcResponse = await supabase.rpc('add_package_item_and_recalculate', rpcParams);
                const { data: newLineItemId, error: rpcError } = rpcResponse;
                if (rpcError) {
                    this.logger.error(`RPC add_package_item_and_recalculate failed during template population for package ${item.package_id} on calc ${calculationId}: ${rpcError.message}`, rpcError.details);
                    continue;
                }
                if (!newLineItemId) {
                    this.logger.error(`RPC add_package_item_and_recalculate for package ${item.package_id} did not return a line item ID during template population.`);
                    continue;
                }
                this.logger.log(`Successfully processed package ${item.package_id} via RPC -> line item ${newLineItemId}`);
            }
            catch (error) {
                const message = error instanceof Error ? error.message : 'Unknown error';
                const stack = error instanceof Error ? error.stack : undefined;
                this.logger.error(`Unexpected error processing package ${item.package_id} for calc ${calculationId} during template population: ${message}`, stack);
                throw new common_1.InternalServerErrorException(`An unexpected error occurred while processing package ${item.package_id}.`);
            }
        }
        this.logger.log(`Finished populating items for calculation ${calculationId}.`);
    }
    async fetchPackageDetails(supabase, packageId) {
        const { data, error } = await supabase
            .from('packages')
            .select('id, name, quantity_basis')
            .eq('id', packageId)
            .single();
        if (error || !data) {
            this.logger.error(`Package details not found for ID ${packageId}: ${error?.message}`);
            throw new common_1.NotFoundException(`Required package details not found for ID ${packageId}.`);
        }
        return data;
    }
    async fetchPackagePrice(supabase, packageId, currencyId) {
        const { data, error } = await supabase
            .from('package_prices')
            .select('price, unit_base_cost')
            .eq('package_id', packageId)
            .eq('currency_id', currencyId)
            .maybeSingle();
        if (error) {
            this.logger.error(`Error fetching price for package ${packageId}, currency ${currencyId}: ${error.message}`);
            throw new common_1.InternalServerErrorException(`Database error fetching price for package ${packageId}.`);
        }
        if (!data) {
            this.logger.warn(`Price not found for package ${packageId}, currency ${currencyId}. Defaulting to 0.`);
            return { price: 0, unit_base_cost: 0 };
        }
        return data;
    }
    async fetchOptionDetails(supabase, optionIds, currencyId) {
        if (!optionIds || optionIds.length === 0) {
            return [];
        }
        const { data, error } = await supabase
            .from('package_options')
            .select('id, option_name, price_adjustment, cost_adjustment')
            .in('id', optionIds)
            .eq('currency_id', currencyId);
        if (error) {
            this.logger.error(`Error fetching options [${optionIds.join(', ')}] for currency ${currencyId}: ${error.message}`);
            throw new common_1.InternalServerErrorException(`Database error fetching option details.`);
        }
        if (!data || data.length !== optionIds.length) {
            this.logger.warn(`Mismatch fetching options for IDs [${optionIds.join(', ')}] and currency ${currencyId}. Some options might be missing or lack price data for this currency.`);
            throw new common_1.NotFoundException(`One or more options [${optionIds.join(', ')}] not found or missing price data for currency ${currencyId}.`);
        }
        return data;
    }
    async addPackageLineItem(calcId, addDto, user) {
        this.logger.log(`User ${user.id} adding package ${addDto.packageId} to calc ${calcId} via RPC.`);
        const supabase = this.supabaseService.getClient();
        await this.checkCalculationOwnership(supabase, calcId, user.id);
        const { data: calcData, error: calcError } = await supabase
            .from('calculation_history')
            .select('currency_id')
            .eq('id', calcId)
            .single();
        if (calcError || !calcData) {
            this.logger.error(`Error fetching calculation ${calcId} before adding item: ${calcError?.message}`);
            throw new common_1.InternalServerErrorException('Could not fetch calculation details.');
        }
        const currencyId = calcData.currency_id;
        const rpcParams = {
            p_calculation_id: calcId,
            p_user_id: user.id,
            p_package_id: addDto.packageId,
            p_option_ids: addDto.optionIds ?? [],
            p_currency_id: currencyId,
            p_quantity_override: addDto.quantity,
            p_duration_override: addDto.duration,
            p_notes: addDto.notes,
        };
        this.logger.debug(`Calling RPC add_package_item_and_recalculate with params: ${JSON.stringify(rpcParams)}`);
        const rpcResponse = await supabase.rpc('add_package_item_and_recalculate', rpcParams);
        const { data: newLineItemId, error: rpcError } = rpcResponse;
        if (rpcError) {
            this.logger.error(`RPC add_package_item_and_recalculate failed for calc ${calcId}: ${rpcError.message}`, rpcError.details);
            if (rpcError.message.includes('Calculation not found')) {
                throw new common_1.NotFoundException(`Calculation with ID ${calcId} not found.`);
            }
            if (rpcError.message.includes('does not own calculation')) {
                throw new common_1.ForbiddenException('User does not own this calculation.');
            }
            if (rpcError.message.includes('Package not found')) {
                throw new common_1.NotFoundException(`Package ID ${addDto.packageId} not found.`);
            }
            if (rpcError.message.includes('Price not found')) {
                throw new common_1.NotFoundException(`Price not found for package ${addDto.packageId} and currency ${currencyId}.`);
            }
            if (rpcError.message.includes('options not found')) {
                throw new common_1.NotFoundException(`One or more options not found for package ${addDto.packageId} and currency ${currencyId}.`);
            }
            throw new common_1.InternalServerErrorException('Failed to add package line item via RPC.');
        }
        if (!newLineItemId) {
            this.logger.error(`RPC add_package_item_and_recalculate for calc ${calcId} did not return a line item ID.`);
            throw new common_1.InternalServerErrorException('Failed to get new line item ID after adding package.');
        }
        this.logger.log(`Successfully added package ${addDto.packageId} via RPC. New line item ID: ${newLineItemId}`);
        return { id: newLineItemId };
    }
    async addCustomLineItem(calcId, addDto, user) {
        this.logger.log(`User ${user.id} adding custom item '${addDto.itemName}' to calculation ${calcId}`);
        const supabase = this.supabaseService.getClient();
        await this.checkCalculationOwnership(supabase, calcId, user.id);
        const { data: calcData, error: calcError } = await supabase
            .from('calculation_history')
            .select('currency_id')
            .eq('id', calcId)
            .single();
        if (calcError || !calcData) {
            throw new common_1.InternalServerErrorException('Failed to retrieve calculation currency for custom item.');
        }
        const insertPayload = {
            calculation_id: calcId,
            item_name: addDto.itemName,
            description: addDto.description,
            item_quantity: addDto.quantity,
            item_quantity_basis: addDto.itemQuantityBasis || 1,
            quantity_basis: addDto.quantityBasis || 'PER_DAY',
            unit_price: addDto.unitPrice,
            unit_cost: addDto.unitCost || 0,
            currency_id: calcData.currency_id,
            category_id: addDto.categoryId,
            city_id: addDto.cityId,
        };
        const insertResponse = await supabase
            .from('calculation_custom_items')
            .insert(insertPayload)
            .select('id')
            .single();
        const { data, error: insertError } = insertResponse;
        const newItem = data;
        if (insertError || !newItem) {
            this.logger.error(`Failed to add custom item to calculation ${calcId}: ${insertError?.message}`, insertError?.stack);
            throw new common_1.InternalServerErrorException('Could not add custom item.');
        }
        await this.recalcCalculationViaRpc(calcId);
        this.logger.log(`Custom item ${newItem.id} added to calculation ${calcId} and recalculation triggered.`);
        return { id: newItem.id };
    }
    async updateLineItem(calcId, itemId, updateDto, user) {
        this.logger.log(`Attempting to update line item ${itemId} in calc ${calcId}`);
        const supabase = this.supabaseService.getClient();
        const currentItem = await this.getLineItemById(calcId, itemId);
        if (currentItem.is_custom) {
            this.logger.log(`Updating custom item ${itemId}`);
            const updatePayload = {};
            if (updateDto.itemName !== undefined)
                updatePayload.item_name = updateDto.itemName;
            if (updateDto.description !== undefined)
                updatePayload.description = updateDto.description;
            if (updateDto.quantity !== undefined)
                updatePayload.item_quantity = updateDto.quantity;
            if (updateDto.unitPrice !== undefined)
                updatePayload.unit_price = updateDto.unitPrice;
            if (updateDto.unitCost !== undefined)
                updatePayload.unit_cost = updateDto.unitCost;
            if (updateDto.itemQuantityBasis !== undefined)
                updatePayload.item_quantity_basis = updateDto.itemQuantityBasis;
            if (updateDto.quantityBasis !== undefined)
                updatePayload.quantity_basis = updateDto.quantityBasis;
            if (updateDto.categoryId !== undefined)
                updatePayload.category_id = updateDto.categoryId;
            updatePayload.updated_at = new Date().toISOString();
            const { data, error } = await supabase
                .from('calculation_custom_items')
                .update(updatePayload)
                .eq('id', itemId)
                .eq('calculation_id', calcId)
                .select('*')
                .single();
            if (error) {
                this.logger.error(`Error updating custom item ${itemId}: ${error.message}`, error.details);
                throw new common_1.InternalServerErrorException('Failed to update custom item.');
            }
            this.logger.log(`Successfully updated custom item ${itemId}`);
        }
        else {
            this.logger.log(`Updating package line item ${itemId}`);
            const updatePayload = {};
            if (updateDto.quantity !== undefined)
                updatePayload.item_quantity = updateDto.quantity;
            if (updateDto.duration_days !== undefined)
                updatePayload.item_quantity_basis = updateDto.duration_days;
            if (updateDto.notes !== undefined)
                updatePayload.notes = updateDto.notes;
            if (updateDto.description !== undefined)
                updatePayload.notes = updateDto.description;
            updatePayload.updated_at = new Date().toISOString();
            const { data, error } = await supabase
                .from('calculation_line_items')
                .update(updatePayload)
                .eq('id', itemId)
                .eq('calculation_id', calcId)
                .select('*')
                .single();
            if (error) {
                this.logger.error(`Error updating package line item ${itemId}: ${error.message}`, error.details);
                throw new common_1.InternalServerErrorException('Failed to update package line item.');
            }
            this.logger.log(`Successfully updated package line item ${itemId}`);
        }
        await this.recalcCalculationViaRpc(calcId);
        return this.getLineItemById(calcId, itemId);
    }
    async deletePackageLineItem(calcId, itemId, user) {
        this.logger.log(`Attempting to delete package item ${itemId} from calc ${calcId} via RPC`);
        const supabase = this.supabaseService.getClient();
        const rpcParams = {
            p_calculation_id: calcId,
            p_item_id: itemId,
            p_user_id: user.id,
            p_item_type: 'package',
        };
        this.logger.debug(`Calling RPC delete_line_item_and_recalculate with params: ${JSON.stringify(rpcParams)}`);
        const { error } = await supabase.rpc('delete_line_item_and_recalculate', rpcParams);
        if (error) {
            this.logger.error(`RPC delete_line_item_and_recalculate failed for package item ${itemId} on calc ${calcId}: ${error.message}`, error.details);
            if (error.message.includes('does not own calculation')) {
                throw new common_1.ForbiddenException('User does not own this calculation.');
            }
            if (error.message.includes('Calculation not found') ||
                error.message.includes('line item not found')) {
                throw new common_1.NotFoundException('Calculation or line item not found.');
            }
            throw new common_1.InternalServerErrorException('Failed to delete package line item.');
        }
        this.logger.log(`Successfully deleted package item ${itemId} from calc ${calcId} via RPC.`);
    }
    async deleteCustomLineItem(calcId, itemId, user) {
        this.logger.log(`Attempting to delete custom item ${itemId} from calc ${calcId} via RPC`);
        const supabase = this.supabaseService.getClient();
        const rpcParams = {
            p_calculation_id: calcId,
            p_item_id: itemId,
            p_user_id: user.id,
            p_item_type: 'custom',
        };
        this.logger.debug(`Calling RPC delete_line_item_and_recalculate with params: ${JSON.stringify(rpcParams)}`);
        const { error } = await supabase.rpc('delete_line_item_and_recalculate', rpcParams);
        if (error) {
            this.logger.error(`RPC delete_line_item_and_recalculate failed for custom item ${itemId} on calc ${calcId}: ${error.message}`, error.details);
            if (error.message.includes('does not own calculation')) {
                throw new common_1.ForbiddenException('User does not own this calculation.');
            }
            if (error.message.includes('Calculation not found') ||
                error.message.includes('line item not found')) {
                throw new common_1.NotFoundException('Calculation or line item not found.');
            }
            throw new common_1.InternalServerErrorException('Failed to delete custom line item.');
        }
        this.logger.log(`Successfully deleted custom item ${itemId} from calc ${calcId} via RPC.`);
    }
    async checkCalculationOwnership(supabase, calculationId, userId) {
        const { data, error } = await supabase
            .from('calculation_history')
            .select('id')
            .eq('id', calculationId)
            .eq('created_by', userId)
            .maybeSingle();
        if (error) {
            this.logger.error(`Error checking ownership for calc ${calculationId}: ${error.message}`);
            throw new common_1.InternalServerErrorException('Ownership check failed.');
        }
        if (!data) {
            this.logger.warn(`User ${userId} attempt to modify calc ${calculationId} without ownership.`);
            throw new common_1.ForbiddenException('Access denied to this calculation.');
        }
    }
    async recalcCalculationViaRpc(calcId) {
        this.logger.log(`Triggering recalculation RPC for calculation ${calcId}`);
        const supabase = this.supabaseService.getClient();
        const { error: rpcError } = await supabase.rpc('recalculate_calculation_totals', { p_calculation_id: calcId });
        if (rpcError) {
            this.logger.error(`RPC recalculate_calculation_totals failed for calc ${calcId}: ${rpcError.message}`, rpcError.details);
            throw new common_1.InternalServerErrorException(`Failed to trigger recalculation for calculation ${calcId}.`);
        }
        this.logger.log(`Successfully triggered recalculation via RPC for calc ID: ${calcId}`);
    }
};
exports.CalculationItemsService = CalculationItemsService;
exports.CalculationItemsService = CalculationItemsService = CalculationItemsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], CalculationItemsService);
//# sourceMappingURL=calculation-items.service.js.map