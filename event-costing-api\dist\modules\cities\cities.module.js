"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CitiesModule = void 0;
const common_1 = require("@nestjs/common");
const cities_service_1 = require("./cities.service");
const cities_controller_1 = require("./cities.controller");
const admin_cities_controller_1 = require("./admin-cities.controller");
const auth_module_1 = require("../auth/auth.module");
const admin_module_1 = require("../auth/admin.module");
let CitiesModule = class CitiesModule {
};
exports.CitiesModule = CitiesModule;
exports.CitiesModule = CitiesModule = __decorate([
    (0, common_1.Module)({
        imports: [auth_module_1.AuthModule, admin_module_1.AdminModule],
        controllers: [cities_controller_1.CitiesController, admin_cities_controller_1.AdminCitiesController],
        providers: [cities_service_1.CitiesService],
    })
], CitiesModule);
//# sourceMappingURL=cities.module.js.map