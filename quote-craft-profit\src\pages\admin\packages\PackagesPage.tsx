import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import AdminLayout from "@/components/layout/AdminLayout";
import { toast } from "sonner";
import { useSearchParams, useNavigate } from "react-router-dom";
import { useSettings } from "@/hooks/useSettings";
import DataPagination from "@/components/ui/data-pagination";
import { getAllCategories } from "@/services/admin/categories";
import { getAllDivisions } from "@/services/admin/divisions";
import { getAllCities } from "@/services/shared/entities/cities";

// Import components from the feature's components directory
import { PackagesTable } from "./components/list/PackagesTable";
import { PackageFilters } from "./components/list/PackageFilters";
import { PackageListHeader } from "./components/list/PackageListHeader";
import { AddPackageDialog } from "./components/form/AddPackageDialog";
import { PackageErrorBoundary } from "./components/shared";

// Import services from the feature's services directory
import { getAllPackages } from "../../../services/admin/packages/packageService";

const PackagesPage: React.FC = () => {
  // Get column settings from context
  const { settings } = useSettings();
  const [searchParams, setSearchParams] = useSearchParams();
  const [isAddPackageOpen, setIsAddPackageOpen] = useState(false);

  // Initialize filters from URL search params
  const [filters, setFilters] = useState({
    categoryId: searchParams.get("category") || "all",
    divisionId: searchParams.get("division") || "all",
    cityId: searchParams.get("city") || "all",
    search: searchParams.get("search") || "",
    showDeleted: searchParams.get("showDeleted") === "true",
    page: parseInt(searchParams.get("page") || "1"),
    pageSize: parseInt(searchParams.get("pageSize") || "10"),
    sortBy: searchParams.get("sortBy") || "updated_at",
    sortOrder: (searchParams.get("sortOrder") || "desc") as "asc" | "desc",
  });

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    if (filters.categoryId !== "all")
      params.set("category", filters.categoryId);
    if (filters.divisionId !== "all")
      params.set("division", filters.divisionId);
    if (filters.cityId !== "all") params.set("city", filters.cityId);
    if (filters.search) params.set("search", filters.search);
    if (filters.showDeleted) params.set("showDeleted", "true");
    if (filters.page !== 1) params.set("page", filters.page.toString());
    if (filters.pageSize !== 10)
      params.set("pageSize", filters.pageSize.toString());
    if (filters.sortBy !== "updated_at") params.set("sortBy", filters.sortBy);
    if (filters.sortOrder !== "desc")
      params.set("sortOrder", filters.sortOrder);

    setSearchParams(params);
  }, [filters, setSearchParams]);

  // Fetch packages with React Query and caching
  const {
    data: packageData,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["packages", filters],
    queryFn: () => getAllPackages(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    meta: {
      onError: () => {
        toast.error("Failed to load packages");
      },
    },
    placeholderData: (previousData) => previousData, // Keep previous data while loading new data
  });

  // Fetch categories for filter dropdown with caching
  const { data: categories = [] } = useQuery({
    queryKey: ["categories"],
    queryFn: getAllCategories,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    meta: {
      onError: () => {
        console.error("Failed to load categories");
      },
    },
  });

  // Fetch divisions for filter dropdown with caching
  const { data: divisions = [] } = useQuery({
    queryKey: ["divisions"],
    queryFn: () => getAllDivisions(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    meta: {
      onError: () => {
        console.error("Failed to load divisions");
      },
    },
  });

  // Fetch cities for filter dropdown with caching
  const { data: cities = [] } = useQuery({
    queryKey: ["cities"],
    queryFn: getAllCities,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    meta: {
      onError: () => {
        console.error("Failed to load cities");
      },
    },
  });

  const handleOpenNewPackageForm = () => {
    setIsAddPackageOpen(true);
  };

  const navigate = useNavigate();

  const handleEditPackage = (packageId: string) => {
    // Navigate to the full edit page instead of opening the dialog
    navigate(`/admin/packages/${packageId}`);
  };

  const handleAddPackageClose = (shouldRefresh: boolean = false) => {
    setIsAddPackageOpen(false);

    if (shouldRefresh) {
      refetch();
    }
  };

  const handleFilterChange = (key: string, value: string | boolean) => {
    setFilters((prev) => ({ ...prev, [key]: value, page: 1 }));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters((prev) => ({ ...prev, search: e.target.value, page: 1 }));
  };

  const handleSortChange = (field: string, order: "asc" | "desc") => {
    setFilters((prev) => ({
      ...prev,
      sortBy: field,
      sortOrder: order,
      page: 1,
    }));
  };

  const clearFilters = () => {
    setFilters({
      ...filters,
      categoryId: "all",
      divisionId: "all",
      cityId: "all",
      search: "",
      showDeleted: false,
      page: 1, // Reset to first page when clearing filters
      sortBy: "updated_at", // Maintain default sort by last modified
      sortOrder: "desc", // Maintain default sort order
    });
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setFilters({
      ...filters,
      page,
    });
  };

  // Handle page size change
  const handlePageSizeChange = (pageSize: number) => {
    setFilters({
      ...filters,
      pageSize,
      page: 1, // Reset to first page when changing page size
    });
  };

  return (
    <AdminLayout title="Manage Packages">
      <PackageErrorBoundary>
        <PackageListHeader
          onAddPackage={handleOpenNewPackageForm}
          totalCount={packageData?.totalCount || 0}
        />

        <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
          <p className="text-blue-700 dark:text-blue-300 text-sm">
            <strong>Tip:</strong> You can edit packages in two ways:
          </p>
          <ul className="text-blue-700 dark:text-blue-300 text-sm list-disc ml-5 mt-2">
            <li>
              Click on a package name to access the full edit page with all
              features including venue selection
            </li>
            <li>
              Click the "Edit" button to quickly navigate to the same full edit
              page
            </li>
          </ul>
        </div>

        <PackageFilters
          filters={filters}
          categories={categories}
          divisions={divisions}
          cities={cities}
          onFilterChange={handleFilterChange}
          onSearchChange={handleSearchChange}
          onClearFilters={clearFilters}
          onSortChange={handleSortChange}
        />

        <PackagesTable
          packages={packageData?.data || []}
          isLoading={isLoading}
          isError={isError}
          onEdit={handleEditPackage}
          onStatusToggle={refetch}
          columnSettings={settings.packageTable}
        />

        {/* Pagination */}
        {packageData && (
          <DataPagination
            currentPage={filters.page}
            totalPages={packageData.totalPages}
            totalItems={packageData.totalCount}
            pageSize={filters.pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            showPageSizeSelector={true}
            pageSizeOptions={[10, 25, 50, 100]}
            className="mt-4"
          />
        )}

        {/* Add Package Dialog */}
        <AddPackageDialog
          isOpen={isAddPackageOpen}
          onClose={handleAddPackageClose}
        />
      </PackageErrorBoundary>
    </AdminLayout>
  );
};

export default PackagesPage;
