/**
 * UI state management hook for calculation detail page
 * Handles UI-specific state like expanded categories, package forms, etc.
 */
import { useMemo } from "react";
import { useCategoryExpansion } from "./useCategoryExpansion";
import { usePackageForms } from "./usePackageForms";
import { useCalculationEditState } from "./useCalculationEditState";
import { useLineItemDialogs } from "../data/useLineItemDialogs";
import { useRenderTracker } from "@/lib/renderTracker";

/**
 * UI state management for calculation detail page
 * Consolidates all UI-related state management
 *
 * @param calculationId - The calculation ID
 * @param calculation - The calculation data
 * @returns UI state and handlers
 */
export const useCalculationDetailUI = (
  calculationId: string,
  calculation: any
) => {
  // Category expansion state
  const { expandedCategories, toggleCategory } = useCategoryExpansion();

  // DEBUG: Track renders to identify infinite loop causes
  useRenderTracker(
    "useCalculationDetailUI",
    {
      calculationId,
      calculationName: calculation?.name,
      calculationId_stable: calculationId,
      hasCalculation: !!calculation,
    },
    { logLevel: "detailed" }
  );

  // Package form state (quantity, item_quantity_basis, options)
  const {
    packageForms,
    handleQuantityChange,
    handleItemQuantityBasisChange,
    handleOptionToggle,
    calculatePackageTotalPrice,
    cleanupPackageForm,
    cleanupMultiplePackageForms,
    resetAllPackageForms,
    getPackageFormData,
  } = usePackageForms();

  // Calculation edit state
  const {
    isEditMode,
    isSaving,
    editedName,
    editedEventType,
    editedNotes,
    editedAttendees,
    dateRange,
    setEditedName,
    setEditedEventType,
    setEditedNotes,
    setEditedAttendees,
    setDateRange,
    handleToggleEditMode,
    handleSaveChanges,
  } = useCalculationEditState(calculationId, calculation);

  // Line item dialog state with package form cleanup integration
  const {
    isAddingCustomItem,
    isEditingLineItem,
    currentEditingLineItem,
    isDeleting,
    setIsAddingCustomItem,
    setIsEditingLineItem,
    handleAddCustomItem,
    handleEditLineItem,
    handleUpdateLineItem,
    handleRemoveLineItem,
  } = useLineItemDialogs(calculationId, cleanupPackageForm);

  return useMemo(
    () => ({
      // Category state
      expandedCategories,
      toggleCategory,

      // Package form state
      packageForms,
      handleQuantityChange,
      handleItemQuantityBasisChange,
      handleOptionToggle,
      calculatePackageTotalPrice,
      cleanupPackageForm,
      cleanupMultiplePackageForms,
      resetAllPackageForms,
      getPackageFormData,

      // Edit mode state
      isEditMode,
      isSaving,
      editedName,
      editedEventType,
      editedNotes,
      editedAttendees,
      dateRange,
      setEditedName,
      setEditedEventType,
      setEditedNotes,
      setEditedAttendees,
      setDateRange,
      handleToggleEditMode,
      handleSaveChanges,

      // Line item dialog state
      isAddingCustomItem,
      isEditingLineItem,
      currentEditingLineItem,
      isDeleting,
      setIsAddingCustomItem,
      setIsEditingLineItem,
      handleAddCustomItem,
      handleEditLineItem,
      handleUpdateLineItem,
      handleRemoveLineItem,
    }),
    [
      // Category state
      expandedCategories,
      toggleCategory,
      // Package form state
      packageForms,
      handleQuantityChange,
      handleItemQuantityBasisChange,
      handleOptionToggle,
      calculatePackageTotalPrice,
      cleanupPackageForm,
      cleanupMultiplePackageForms,
      resetAllPackageForms,
      getPackageFormData,
      // Edit mode state
      isEditMode,
      isSaving,
      editedName,
      editedEventType,
      editedNotes,
      editedAttendees,
      dateRange,
      setEditedName,
      setEditedEventType,
      setEditedNotes,
      setEditedAttendees,
      setDateRange,
      handleToggleEditMode,
      handleSaveChanges,
      // Line item dialog state
      isAddingCustomItem,
      isEditingLineItem,
      currentEditingLineItem,
      isDeleting,
      setIsAddingCustomItem,
      setIsEditingLineItem,
      handleAddCustomItem,
      handleEditLineItem,
      handleUpdateLineItem,
      handleRemoveLineItem,
    ]
  );
};
