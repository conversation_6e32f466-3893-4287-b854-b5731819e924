/**
 * Comprehensive calculation detail hook
 * PHASE 4 OPTIMIZATION: Consolidates all calculation detail state and actions
 * 
 * This hook combines:
 * - Core calculation data (useCalculationDetail)
 * - Taxes and discounts state (useTaxesAndDiscounts)
 * - Calculation actions (useCalculationActions)
 * 
 * Eliminates the need for manual state combination in components
 */

import { useMemo } from "react";
import { useCalculationDetail } from "./useCalculationDetail";
import { useTaxesAndDiscounts } from "../financial/useTaxesAndDiscounts";
import { useCalculationActions } from "../useCalculationActions";

/**
 * Complete calculation detail hook that combines all necessary state and actions
 * @param id - The calculation ID
 * @returns Combined state and actions for the calculation detail page
 */
export const useCalculationDetailComplete = (id: string) => {
  // Core calculation data with optimized parallel loading
  const calculationDetail = useCalculationDetail(id);
  const { calculation, isLoading, isError } = calculationDetail;

  // Taxes and discounts state management
  const taxesAndDiscounts = useTaxesAndDiscounts(
    id,
    calculation?.taxes,
    calculation?.discount
  );

  // Calculation actions (status changes, deletion, navigation)
  const actions = useCalculationActions({
    calculationId: id,
    saveTaxesAndDiscount: taxesAndDiscounts.saveTaxesAndDiscount,
  });

  // Combine all state into a single object (memoized for performance)
  const combinedState = useMemo(() => {
    return {
      ...calculationDetail,
      ...taxesAndDiscounts,
    };
  }, [calculationDetail, taxesAndDiscounts]);

  // Return the complete state and actions
  return {
    // Combined state
    state: combinedState,
    
    // Actions
    actions,
    
    // Individual pieces for convenience
    calculation,
    isLoading,
    isError,
    
    // Calculation ID for context provider
    calculationId: id,
  };
};
