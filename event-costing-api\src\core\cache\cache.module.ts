import { Module, Global } from '@nestjs/common';
import {
  CacheModule as NestCacheModule,
  CacheModuleOptions,
} from '@nestjs/cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as redisStore from 'cache-manager-redis-store';
import { CacheService } from './cache.service';

@Global()
@Module({
  imports: [
    NestCacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService): CacheModuleOptions => {
        const isRedisEnabled =
          configService.get<string>('REDIS_HOST') !== undefined;

        if (isRedisEnabled) {
          return {
            store: redisStore,
            host: configService.get<string>('REDIS_HOST'),
            port: configService.get<number>('REDIS_PORT'),
            username: configService.get<string>('REDIS_USERNAME'),
            password: configService.get<string>('REDIS_PASSWORD'),
            ttl: 60 * 60 * 24, // 24 hours default TTL
            max: 1000, // maximum number of items in cache
          };
        } else {
          // Fallback to in-memory cache if Redis is not configured
          return {
            store: 'memory',
            ttl: 60 * 60, // 1 hour default TTL for in-memory cache
            max: 500, // maximum number of items in cache
          };
        }
      },
    }),
  ],
  providers: [CacheService],
  exports: [NestCacheModule, CacheService],
})
export class CacheModule {}
