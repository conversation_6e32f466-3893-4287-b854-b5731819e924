// Package-specific constants
export const QUANTITY_BASIS_OPTIONS = [
  { value: 'PER_EVENT', label: 'Per Event' },
  { value: 'PER_DAY', label: 'Per Day' },
  { value: 'PER_ATTENDEE', label: 'Per Attendee' },
  { value: 'PER_ITEM', label: 'Per Item' },
  { value: 'PER_ITEM_PER_DAY', label: 'Per Item Per Day' },
  { value: 'PER_ATTENDEE_PER_DAY', label: 'Per Attendee Per Day' },
];

// Query keys for React Query
export const QUERY_KEYS = {
  packages: (filters?: any) => ['packages', filters],
  package: (id: string) => ['package', id],
  packageOptions: (packageId: string) => ['packageOptions', packageId],
  packageDependencies: (packageId: string) => ['packageDependencies', packageId],
  packageCities: (packageId: string) => ['packageCities', packageId],
  packageVenues: (packageId: string) => ['packageVenues', packageId],
};
