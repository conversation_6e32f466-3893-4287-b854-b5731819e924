import React from 'react';
import { Button } from '@/components/ui/button';
import { CurrencyInput } from '@/components/ui/currency-input';
import { Plus, X } from 'lucide-react';

interface DiscountFormProps {
  discountAmount: string;
  onDiscountAmountChange: (value: string) => void;
  onAdd: () => void;
  onCancel: () => void;
}

/**
 * Discount addition form with validation
 * Provides input field for discount amount with add/cancel actions
 */
export const DiscountForm: React.FC<DiscountFormProps> = ({
  discountAmount,
  onDiscountAmountChange,
  onAdd,
  onCancel,
}) => {
  return (
    <div className='flex flex-col space-y-2 pb-2 border-b'>
      <div className='flex space-x-2'>
        <CurrencyInput
          placeholder='Discount amount'
          value={discountAmount}
          onChange={(numericValue) => onDiscountAmountChange(numericValue.toString())}
          className='flex-1'
          showSymbol={false}
        />
      </div>
      <div className='flex justify-end space-x-2'>
        <Button
          variant='outline'
          size='sm'
          onClick={onCancel}
        >
          <X size={16} className='mr-1' /> Cancel
        </Button>
        <Button variant='default' size='sm' onClick={onAdd}>
          <Plus size={16} className='mr-1' /> Add
        </Button>
      </div>
    </div>
  );
};
