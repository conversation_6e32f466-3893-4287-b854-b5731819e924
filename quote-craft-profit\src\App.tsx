import { Toaster as Sonner } from "@/components/ui/sonner";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import CalculationsPage from "./pages/calculations/CalculationsPage";
import NewCalculationPage from "./pages/calculations/NewCalculationPage";
import CalculationDetailPage from "./pages/calculations/CalculationDetailPage";
import ClientsPage from "./pages/clients/ClientsPage";
import ClientDetailsPage from "./pages/clients/ClientDetailsPage";
import ReportsPage from "./pages/reports/ReportsPage";
import { AuthPage } from "./pages/auth";

// Using direct Supabase auth provider
import { AuthProvider } from "./contexts/AuthContext";
import { SettingsProvider } from "./contexts/SettingsContext";
import { OnboardingProvider } from "./contexts/OnboardingContext";
import { NotificationProvider } from "./contexts/NotificationContext";

import { ProtectedRoute, AdminProtectedRoute } from "./pages/auth";

import ProfilePage from "./pages/profile/ProfilePage";
import ProfileSettingsPage from "./pages/profile/ProfileSettingsPage";
import EventsPage from "./pages/events/EventsPage";
import EventDetailsPage from "./pages/events/EventDetailsPage";
import NewEventPage from "./pages/events/NewEventPage";
import UserTemplatesPage from "./pages/templates/TemplatesPage";
import TemplateDetailPage from "./pages/templates/TemplateDetailPage";
import { DashboardV2Page } from "./pages/dashboard-v2";

// Admin Pages
import { AdminDashboardPage } from "./pages/admin/dashboard";
import { UsersPage } from "./pages/admin/users";
import CataloguePage from "./pages/admin/CataloguePage";
import { CategoriesPage } from "./pages/admin/categories";
import { DivisionsPage } from "./pages/admin/divisions";
import { CitiesPage } from "./pages/admin/cities";
import { PackagesPage, EditPackagePage } from "./pages/admin/packages";
import { TemplatesPage } from "./pages/admin/templates";
import { SettingsPage } from "./pages/admin/settings";
import { VenuesPage } from "./pages/admin/venues";
import EventTypesPage from "./pages/admin/event-types/EventTypesPage";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Ensure fresh data for critical operations
      staleTime: 1000 * 60 * 5, // 5 minutes default
      gcTime: 1000 * 60 * 10, // 10 minutes default
      refetchOnWindowFocus: false, // Disable automatic refetch on window focus
      retry: 2, // Retry failed requests twice
    },
    mutations: {
      retry: 1, // Retry failed mutations once
    },
  },
});

const App = () => (
  <QueryClientProvider client={queryClient}>
    <Sonner />
    <BrowserRouter>
      <AuthProvider>
        <SettingsProvider>
          <NotificationProvider>
            <OnboardingProvider>
              <Routes>
                <Route path="/auth" element={<AuthPage />} />
                <Route
                  path="/"
                  element={
                    <ProtectedRoute>
                      <Index />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard-v2"
                  element={
                    <ProtectedRoute>
                      <DashboardV2Page />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/calculations"
                  element={
                    <ProtectedRoute>
                      <CalculationsPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/calculations/new"
                  element={
                    <ProtectedRoute>
                      <NewCalculationPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/calculations/from-template/:templateId"
                  element={
                    <ProtectedRoute>
                      <NewCalculationPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/calculations/:id"
                  element={
                    <ProtectedRoute>
                      <CalculationDetailPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/clients"
                  element={
                    <ProtectedRoute>
                      <ClientsPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/clients/:id"
                  element={
                    <ProtectedRoute>
                      <ClientDetailsPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/reports"
                  element={
                    <ProtectedRoute>
                      <ReportsPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/events"
                  element={
                    <ProtectedRoute>
                      <EventsPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/events/new"
                  element={
                    <ProtectedRoute>
                      <NewEventPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/events/:id"
                  element={
                    <ProtectedRoute>
                      <EventDetailsPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/profile"
                  element={
                    <ProtectedRoute>
                      <ProfilePage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/profile/settings"
                  element={
                    <ProtectedRoute>
                      <ProfileSettingsPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/templates"
                  element={
                    <ProtectedRoute>
                      <UserTemplatesPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/templates/:id"
                  element={
                    <ProtectedRoute>
                      <TemplateDetailPage />
                    </ProtectedRoute>
                  }
                />

                {/* Admin Routes - Simplified structure without nested Routes */}
                <Route
                  path="/admin"
                  element={
                    <AdminProtectedRoute>
                      <AdminDashboardPage />
                    </AdminProtectedRoute>
                  }
                />
                <Route
                  path="/admin/users"
                  element={
                    <AdminProtectedRoute>
                      <UsersPage />
                    </AdminProtectedRoute>
                  }
                />
                <Route
                  path="/admin/catalogue"
                  element={
                    <AdminProtectedRoute>
                      <CataloguePage />
                    </AdminProtectedRoute>
                  }
                />
                <Route
                  path="/admin/categories"
                  element={
                    <AdminProtectedRoute>
                      <CategoriesPage />
                    </AdminProtectedRoute>
                  }
                />
                <Route
                  path="/admin/divisions"
                  element={
                    <AdminProtectedRoute>
                      <DivisionsPage />
                    </AdminProtectedRoute>
                  }
                />
                <Route
                  path="/admin/cities"
                  element={
                    <AdminProtectedRoute>
                      <CitiesPage />
                    </AdminProtectedRoute>
                  }
                />
                <Route
                  path="/admin/packages"
                  element={
                    <AdminProtectedRoute>
                      <PackagesPage />
                    </AdminProtectedRoute>
                  }
                />
                <Route
                  path="/admin/packages/:id"
                  element={
                    <AdminProtectedRoute>
                      <EditPackagePage />
                    </AdminProtectedRoute>
                  }
                />
                <Route
                  path="/admin/templates"
                  element={
                    <AdminProtectedRoute>
                      <TemplatesPage />
                    </AdminProtectedRoute>
                  }
                />
                <Route
                  path="/admin/settings"
                  element={
                    <AdminProtectedRoute>
                      <SettingsPage />
                    </AdminProtectedRoute>
                  }
                />
                <Route
                  path="/admin/venues"
                  element={
                    <AdminProtectedRoute>
                      <VenuesPage />
                    </AdminProtectedRoute>
                  }
                />
                <Route
                  path="/admin/event-types"
                  element={
                    <AdminProtectedRoute>
                      <EventTypesPage />
                    </AdminProtectedRoute>
                  }
                />

                <Route path="*" element={<NotFound />} />
              </Routes>
            </OnboardingProvider>
          </NotificationProvider>
        </SettingsProvider>
      </AuthProvider>
    </BrowserRouter>
  </QueryClientProvider>
);

export default App;
