import { Module } from '@nestjs/common';
import { TemplatesService } from './templates.service';
import { TemplatesController } from './templates.controller';
import { AdminModule } from '../auth/admin.module'; // Provides AdminRoleGuard
import { AdminTemplatesController } from './admin-templates.controller'; // Will be created
import { CalculationsModule } from '../calculations/calculations.module'; // Provides CalculationItemsService
import { AuthModule } from '../auth/auth.module'; // Provides JwtAuthGuard
import {
  TemplateQueryService,
  TemplateCreationService,
  TemplateDetailService,
  TemplateAdminService,
  TemplateVenueService,
  TemplateCalculationService,
} from './services';

@Module({
  imports: [AuthModule, AdminModule, CalculationsModule],
  controllers: [TemplatesController, AdminTemplatesController],
  providers: [
    TemplatesService,
    TemplateQueryService,
    TemplateCreationService,
    TemplateDetailService,
    TemplateAdminService,
    TemplateVenueService,
    TemplateCalculationService,
  ],
  exports: [TemplatesService],
})
export class TemplatesModule {}
