import { useState, useEffect, useCallback, useMemo } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Tax, Discount } from "../../utils/calculationUtils";
import { updateCalculation } from "../../../../services/calculations";
import { QUERY_KEYS } from "@/lib/queryKeys";
import { debug } from "@/lib/debugUtils";

/**
 * Custom hook for managing taxes and discounts in calculations
 *
 * This hook centralizes all tax and discount functionality in one place,
 * reducing complexity and improving performance.
 *
 * @param calculationId - The ID of the calculation
 * @param initialTaxes - Initial taxes from the calculation
 * @param initialDiscount - Initial discount from the calculation
 * @returns Object with taxes, discount, and functions to manage them
 */
export function useTaxesAndDiscounts(
  calculationId: string,
  initialTaxes: Tax[] = [],
  initialDiscount?: Discount | null
) {
  const queryClient = useQueryClient();

  // State for taxes and discount
  const [taxes, setTaxes] = useState<Tax[]>(initialTaxes);
  const [discount, setDiscount] = useState<Discount>(
    initialDiscount || {
      name: "Discount",
      type: "fixed",
      value: 0,
    }
  );

  // Update state when initialTaxes or initialDiscount change
  useEffect(() => {
    if (Array.isArray(initialTaxes)) {
      setTaxes(initialTaxes);
    } else {
      setTaxes([]);
    }

    if (initialDiscount) {
      setDiscount(initialDiscount);
    } else {
      setDiscount({
        name: "Discount",
        type: "fixed",
        value: 0,
      });
    }
  }, [initialTaxes, initialDiscount]);

  // Mutation for updating taxes and discount
  const updateMutation = useMutation({
    mutationFn: (data: {
      status?: "draft" | "completed" | "canceled";
      taxes: Tax[];
      discount: Discount;
    }) => updateCalculation(calculationId, data),
    onSuccess: () => {
      // Invalidate the calculation query to ensure fresh data
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculation(calculationId),
      });
    },
    onError: (error) => {
      console.error("Error updating taxes and discount:", error);
      toast.error("Failed to save taxes and discount");
    },
  });

  // Add a new tax
  const addTax = useCallback(
    (newTax: Tax) => {
      const updatedTaxes = [...taxes, newTax];
      setTaxes(updatedTaxes);
      return updatedTaxes;
    },
    [taxes]
  );

  // Remove a tax
  const removeTax = useCallback(
    (taxId: string) => {
      const updatedTaxes = taxes.filter((tax) => tax.id !== taxId);
      setTaxes(updatedTaxes);
      return updatedTaxes;
    },
    [taxes]
  );

  // Update discount
  const updateDiscount = useCallback((newDiscount: Discount) => {
    setDiscount(newDiscount);
    return newDiscount;
  }, []);

  // Save taxes and discount to the database
  const saveTaxesAndDiscount = useCallback(
    async (status?: "draft" | "completed" | "canceled") => {
      try {
        const updateData = {
          ...(status ? { status } : {}),
          taxes,
          discount,
        };

        debug("Saving taxes and discount:", updateData);

        await updateMutation.mutateAsync(updateData);
        return true;
      } catch (error) {
        debug("Error saving taxes and discount:", error);
        return false;
      }
    },
    [taxes, discount, updateMutation]
  );

  return useMemo(
    () => ({
      taxes,
      discount,
      addTax,
      removeTax,
      updateDiscount,
      saveTaxesAndDiscount,
      isUpdating: updateMutation.isPending,
    }),
    [
      taxes,
      discount,
      addTax,
      removeTax,
      updateDiscount,
      saveTaxesAndDiscount,
      updateMutation.isPending,
    ]
  );
}
