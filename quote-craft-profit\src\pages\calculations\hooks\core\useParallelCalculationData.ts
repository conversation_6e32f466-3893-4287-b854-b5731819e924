/**
 * Parallel query optimization hook for calculation detail data
 * Uses useQueries to fetch all data simultaneously, reducing page load time
 */
import { useQueries } from "@tanstack/react-query";
import { useMemo, useCallback } from "react";
import {
  getCalculationById,
  getCalculationLineItems,
  getPackagesByCategoryForCalculation,
} from "../../../../services/calculations";
import { getAllCategories } from "@/services/admin/categories";
import { QUERY_KEYS } from "@/lib/queryKeys";

/**
 * Configuration for parallel data fetching
 */
interface ParallelDataConfig {
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
  retryDelay?: number;
  maxRetries?: number;
}

/**
 * Hook for parallel calculation data fetching
 * Fetches all required data simultaneously to eliminate waterfall requests
 */
export const useParallelCalculationData = (
  calculationId: string,
  config: ParallelDataConfig = {}
) => {
  const {
    enabled = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    gcTime = 10 * 60 * 1000, // 10 minutes
    retryDelay = 1000,
    maxRetries = 3,
  } = config;

  // Define all queries to run in parallel
  const queries = useMemo(
    () => [
      // Query 1: Calculation data
      {
        queryKey: QUERY_KEYS.calculation(calculationId),
        queryFn: () => getCalculationById(calculationId),
        enabled: enabled && !!calculationId,
        staleTime,
        gcTime,
        retryDelay,
        retry: maxRetries,
        meta: {
          name: "calculation",
          onError: (error: Error) => {
            console.error(
              `Failed to fetch calculation ${calculationId}:`,
              error
            );
          },
        },
      },

      // Query 2: Packages by category
      {
        queryKey: QUERY_KEYS.packagesByCategory(calculationId),
        queryFn: () => getPackagesByCategoryForCalculation(calculationId, true),
        enabled: enabled && !!calculationId,
        staleTime,
        gcTime,
        retryDelay,
        retry: maxRetries,
        meta: {
          name: "packagesByCategory",
          onError: (error: Error) => {
            console.error("Failed to fetch packages by category:", error);
          },
        },
      },

      // Query 3: Line items
      {
        queryKey: QUERY_KEYS.lineItems(calculationId),
        queryFn: () => getCalculationLineItems(calculationId),
        enabled: enabled && !!calculationId,
        staleTime,
        gcTime,
        retryDelay,
        retry: maxRetries,
        meta: {
          name: "lineItems",
          onError: (error: Error) => {
            console.error(
              `Failed to fetch line items for calculation ${calculationId}:`,
              error
            );
          },
        },
      },

      // Query 4: Categories
      {
        queryKey: ["categories"],
        queryFn: getAllCategories,
        enabled,
        staleTime: 10 * 60 * 1000, // Categories change less frequently
        gcTime: 20 * 60 * 1000,
        retryDelay,
        retry: maxRetries,
        meta: {
          name: "categories",
          onError: (error: Error) => {
            console.error("Failed to fetch categories:", error);
          },
        },
      },
    ],
    [calculationId, enabled, staleTime, gcTime, retryDelay, maxRetries]
  );

  // Memoize the combine function to prevent infinite re-renders
  const combineFunction = useCallback((results: any[]) => {
    // Extract individual results
    const [
      calculationResult,
      packagesResult,
      lineItemsResult,
      categoriesResult,
    ] = results;

    // Calculate aggregate states
    const isLoading = results.some((result) => result.isLoading);
    const isError = results.some((result) => result.isError);
    const isFetching = results.some((result) => result.isFetching);
    const isSuccess = results.every((result) => result.isSuccess);

    // Calculate individual loading states
    const isLoadingCalculation = calculationResult.isLoading;
    const isLoadingPackages = packagesResult.isLoading;
    const isLoadingLineItems = lineItemsResult.isLoading;
    const isLoadingCategories = categoriesResult.isLoading;

    // Calculate individual error states
    const isErrorCalculation = calculationResult.isError;
    const isPackagesError = packagesResult.isError;
    const isErrorLineItems = lineItemsResult.isError;
    const isErrorCategories = categoriesResult.isError;

    // Extract data with fallbacks
    const calculation = calculationResult.data || null;
    const packagesByCategory = packagesResult.data || [];
    const lineItems = lineItemsResult.data || [];
    const categories = categoriesResult.data || [];

    // Performance metrics
    const loadingStates = {
      calculation: isLoadingCalculation,
      packages: isLoadingPackages,
      lineItems: isLoadingLineItems,
      categories: isLoadingCategories,
    };

    const errorStates = {
      calculation: isErrorCalculation,
      packages: isPackagesError,
      lineItems: isErrorLineItems,
      categories: isErrorCategories,
    };

    return {
      // Core data
      calculation,
      packagesByCategory,
      lineItems,
      categories,

      // Aggregate states
      isLoading,
      isError,
      isFetching,
      isSuccess,

      // Individual loading states
      isLoadingCalculation,
      isLoadingPackages,
      isLoadingLineItems,
      isLoadingCategories,

      // Individual error states
      isErrorCalculation,
      isPackagesError,
      isErrorLineItems,
      isErrorCategories,

      // Performance metrics
      loadingStates,
      errorStates,

      // Raw results for advanced use cases
      rawResults: results,
    };
  }, []);

  // Execute all queries in parallel
  const results = useQueries({
    queries,
    combine: combineFunction,
  });

  return results;
};

/**
 * Hook for optimized calculation detail data with intelligent fallbacks
 * Provides the same interface as useCalculationDetailCore but with parallel loading
 */
export const useOptimizedCalculationDetailCore = (
  calculationId: string,
  config: ParallelDataConfig = {}
) => {
  const parallelData = useParallelCalculationData(calculationId, config);

  // Memoize the result to prevent unnecessary re-renders
  const result = useMemo(() => {
    const returnValue = {
      // Core data (same interface as original hook)
      calculation: parallelData.calculation,
      packagesByCategory: parallelData.packagesByCategory,
      lineItems: parallelData.lineItems,
      categories: parallelData.categories,

      // Loading and error states (same interface as original hook)
      isLoading: parallelData.isLoading,
      isLoadingCalculation: parallelData.isLoadingCalculation,
      isLoadingPackages: parallelData.isLoadingPackages,
      isLoadingLineItems: parallelData.isLoadingLineItems,
      isLoadingCategories: parallelData.isLoadingCategories,
      isError: parallelData.isError,
      isErrorCalculation: parallelData.isErrorCalculation,
      isErrorLineItems: parallelData.isErrorLineItems,
      isErrorCategories: parallelData.isErrorCategories,
      isPackagesError: parallelData.isPackagesError,

      // Additional performance data
      isFetching: parallelData.isFetching,
      isSuccess: parallelData.isSuccess,
      loadingStates: parallelData.loadingStates,
      errorStates: parallelData.errorStates,
    };

    return returnValue;
  }, [parallelData]);

  return result;
};

/**
 * Hook for selective parallel data fetching
 * Allows fetching only specific data types for optimization
 */
export const useSelectiveParallelData = (
  calculationId: string,
  options: {
    fetchCalculation?: boolean;
    fetchPackages?: boolean;
    fetchLineItems?: boolean;
    fetchCategories?: boolean;
  } = {},
  config: ParallelDataConfig = {}
) => {
  const {
    fetchCalculation = true,
    fetchPackages = true,
    fetchLineItems = true,
    fetchCategories = true,
  } = options;

  return useParallelCalculationData(calculationId, {
    ...config,
    enabled:
      config.enabled !== false &&
      (fetchCalculation || fetchPackages || fetchLineItems || fetchCategories),
  });
};
