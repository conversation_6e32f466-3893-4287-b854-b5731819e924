/**
 * Centralized query keys for React Query
 *
 * This file contains all query keys used throughout the application
 * to ensure consistency and avoid typos.
 */

export const QUERY_KEYS = {
  // Calculation keys
  calculation: (id: string) => ['calculation', id],
  calculations: () => ['calculations'],
  calculationsByStatus: (status: string) => ['calculations', { status }],

  // Line item keys
  lineItems: (calculationId: string) => ['calculation', calculationId, 'lineItems'],
  lineItem: (calculationId: string, lineItemId: string) =>
    ['calculation', calculationId, 'lineItem', lineItemId],
  lineItemOptions: (calculationId: string, lineItemId: string) =>
    ['calculation', calculationId, 'lineItem', lineItemId, 'options'],

  // Custom item keys
  customItems: (calculationId: string) => ['calculation', calculationId, 'customItems'],
  customItem: (calculationId: string, itemId: string) =>
    ['calculation', calculationId, 'customItem', itemId],

  // Package keys
  packagesByCategory: (calculationId: string) =>
    ['calculation', calculationId, 'packagesByCategory'],

  // Category keys
  categories: () => ['categories'],

  // Client keys
  clients: () => ['clients'],
  client: (id: string) => ['client', id],

  // Venue keys
  venues: (cityId?: string) => cityId ? ['venues', { cityId }] : ['venues'],
  venue: (id: string) => ['venue', id],

  // City keys
  cities: () => ['cities'],
  city: (id: string) => ['city', id],
};
