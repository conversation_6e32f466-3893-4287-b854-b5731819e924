/**
 * Main calculation detail page
 * PHASE 4 OPTIMIZATION: Simplified state combination logic and streamlined hook chain
 * PHASE 1 OPTIMIZATION: Merged with container component to reduce 4-layer hierarchy to 3 layers
 * Eliminates unnecessary wrapper component while preserving all functionality
 */
import React from "react";
import { useParams } from "react-router-dom";
import MainLayout from "@/components/layout/MainLayout";
import {
  useRenderTracker,
  useObjectReferenceTracker,
} from "@/lib/renderTracker";

// Import hooks - PHASE 4 OPTIMIZATION: Using comprehensive hook
import { useCalculationDetailComplete } from "./hooks/core/useCalculationDetailComplete";

// Import components
import CalculationDetailHeader from "./components/detail/layout/CalculationDetailHeader";
import CalculationDetailContent from "./components/detail/layout/CalculationDetailContent";
import CalculationDetailError from "./components/detail/CalculationDetailError";
import CalculationErrorBoundary from "./components/common/CalculationErrorBoundary";
import CalculationDetailSkeleton from "./components/common/CalculationDetailSkeleton";

// Import context provider
import { CalculationProvider } from "./contexts";

const CalculationDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  // PHASE 4 OPTIMIZATION: Use comprehensive hook for simplified state management
  const { state, actions, calculation, isLoading, isError } =
    useCalculationDetailComplete(id || "");

  // DEBUG: Track main component renders and state changes
  useRenderTracker(
    "CalculationDetailPage",
    {
      id,
      hasState: !!state,
      hasActions: !!actions,
      hasCalculation: !!calculation,
      isLoading,
      isError,
      calculationName: calculation?.name,
    },
    { logLevel: "detailed" }
  );

  useObjectReferenceTracker("state", state);
  useObjectReferenceTracker("actions", actions);

  if (!id) {
    return (
      <MainLayout>
        <CalculationDetailError message="Calculation ID is required" />
      </MainLayout>
    );
  }

  // Show loading state with enhanced skeleton
  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6">
          <CalculationDetailSkeleton />
        </div>
      </MainLayout>
    );
  }

  // Show error state
  if (isError || !calculation) {
    return (
      <MainLayout>
        <CalculationDetailError
          message="Error loading calculation details"
          onNavigateBack={actions.handleNavigateBack}
        />
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <CalculationErrorBoundary
        calculationId={id}
        onRetry={() => window.location.reload()}
        onNavigateBack={actions.handleNavigateBack}
      >
        {/* Header */}
        <CalculationDetailHeader
          name={calculation.name}
          status={calculation.status}
          onNavigateBack={actions.handleNavigateBack}
        />

        {/* Main Content with Context Provider */}
        <CalculationProvider
          calculationId={id}
          state={state as any} // TODO: Fix type compatibility
          actions={actions}
        >
          <CalculationDetailContent />
        </CalculationProvider>
      </CalculationErrorBoundary>
    </MainLayout>
  );
};

export default CalculationDetailPage;
