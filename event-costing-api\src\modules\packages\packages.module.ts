import { Module } from '@nestjs/common';
import { PackagesService } from './packages.service';
import { PackagesController } from './packages.controller';
import { AuthModule } from '../auth/auth.module'; // For protecting controller

@Module({
  imports: [AuthModule], // Needed for JwtAuthGuard
  controllers: [PackagesController],
  providers: [PackagesService],
  exports: [PackagesService], // Export PackagesService so it can be used in other modules
})
export class PackagesModule {}
